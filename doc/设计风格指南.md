# 合同智能审核系统 - 设计风格指南

## 📋 文档概述

本文档定义了合同智能审核系统的完整设计规范，适用于企业级B端法务系统的界面设计。旨在确保产品设计的一致性、专业性和可用性。

**版本**: v1.0  
**更新时间**: 2024年8月6日  
**适用范围**: 法务系统、合规管理、智能审核等企业级产品

---

## 🎯 设计理念

### 核心定位
- **产品类型**: 企业级B端法务系统
- **设计风格**: 现代简约 + 专业严谨
- **目标用户**: 法务人员、律师、合规专员
- **使用场景**: 合同审核、风险识别、版本对比、报告生成

### 设计原则
1. **专业性优先**: 体现法务行业的严谨性和权威性
2. **效率导向**: 简化操作流程，提升工作效率
3. **信息清晰**: 层次分明的信息架构，降低认知负担
4. **一致性**: 统一的视觉语言和交互模式
5. **可访问性**: 确保不同用户群体的使用体验

---

## 🎨 视觉设计系统

### 配色方案

#### 主色调 - 专业蓝色系
```css
/* 品牌主色 */
--primary-blue: #1B4B8C;      /* 深蓝色，象征专业与信任 */
--primary-light: #2E5FA3;     /* 浅蓝色，用于悬停状态 */
--primary-dark: #0F3A6B;      /* 深蓝色，用于按下状态 */

/* 使用场景 */
- 主要按钮背景色
- 导航激活状态
- 链接文字颜色
- 品牌标识色
```

#### 功能色彩 - 风险等级标识
```css
/* 风险等级色彩 */
--risk-high: #E53E3E;         /* 高风险红色 */
--risk-medium: #F6AD55;       /* 中风险橙黄色 */
--risk-low: #38A169;          /* 低风险绿色 */

/* 使用场景 */
- 风险条款标注背景
- 统计图表数据展示
- 状态指示器颜色
- 警告提示信息
```

#### 中性色系 - 界面基础色
```css
/* 背景色系 */
--gray-50: #F7FAFC;           /* 页面背景色 */
--gray-100: #EDF2F7;          /* 卡片悬停背景 */
--gray-200: #E2E8F0;          /* 边框色、分割线 */

/* 文字色系 */
--gray-600: #718096;          /* 辅助文字色 */
--gray-800: #2D3748;          /* 主文字色 */

/* 纯色 */
--white: #FFFFFF;             /* 卡片背景色、按钮文字 */
```

### 字体系统

#### 字体族
```css
font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
```

#### 字号层级
| 用途 | 字号 | 字重 | 行高 | 使用场景 |
|------|------|------|------|----------|
| 页面标题 | 18px | 600 | 1.4 | 页面主标题、模块标题 |
| 区块标题 | 16px | 500 | 1.5 | 卡片标题、表单标题 |
| 正文内容 | 14px | 400 | 1.6 | 正文、列表项、按钮文字 |
| 辅助信息 | 12px | 400 | 1.5 | 提示文字、时间戳、标签 |

#### 特殊字体样式
```css
/* 数字强调 */
.number-emphasis {
    font-size: 32px;
    font-weight: 700;
    line-height: 1.2;
}

/* 代码文字 */
.code-text {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 13px;
}
```

### 间距系统

#### 标准间距单位
```css
/* 基础间距 */
--spacing-xs: 4px;            /* 极小间距 */
--spacing-sm: 8px;            /* 小间距 */
--spacing-md: 12px;           /* 中等间距 */
--spacing-lg: 16px;           /* 大间距 */
--spacing-xl: 20px;           /* 超大间距 */
--spacing-xxl: 24px;          /* 区块间距 */

/* 应用规则 */
- 组件内边距: 12px, 16px, 20px, 24px
- 组件外边距: 8px, 12px, 16px, 24px
- 区块间距: 24px
- 页面边距: 24px(桌面), 16px(移动)
```

### 圆角系统
```css
/* 圆角规范 */
--radius-sm: 4px;             /* 小圆角：标签、徽章 */
--radius-md: 8px;             /* 中圆角：按钮、输入框 */
--radius-lg: 12px;            /* 大圆角：卡片、面板 */
--radius-full: 50%;           /* 圆形：头像、状态点 */
```

### 阴影系统
```css
/* 阴影层级 */
--shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);           /* 轻微阴影 */
--shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);           /* 中等阴影 */
--shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.1);          /* 悬停阴影 */
--shadow-xl: 0 8px 25px rgba(0, 0, 0, 0.15);         /* 弹窗阴影 */

/* 使用场景 */
- 卡片默认: shadow-sm
- 卡片悬停: shadow-lg
- 弹窗模态: shadow-xl
- 下拉菜单: shadow-md
```

---

## 🏗️ 布局设计规范

### 整体架构
```css
/* 页面布局 */
.page-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 顶部导航 */
.header {
    height: 64px;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
}

/* 主体容器 */
.main-container {
    display: flex;
    flex: 1;
}

/* 侧边栏 */
.sidebar {
    width: 280px;
    background: var(--white);
    border-right: 1px solid var(--gray-200);
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 24px;
    background: var(--gray-50);
}
```

### 响应式断点
```css
/* 断点定义 */
--breakpoint-mobile: 768px;
--breakpoint-tablet: 1024px;
--breakpoint-desktop: 1200px;

/* 响应式规则 */
@media (max-width: 768px) {
    .sidebar { width: 100%; }
    .content-area { padding: 16px; }
}

@media (max-width: 1024px) {
    .sidebar { width: 240px; }
}
```

---

## 🎭 组件设计规范

### 按钮组件

#### 主要按钮
```css
.btn-primary {
    background: var(--primary-blue);
    color: var(--white);
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 14px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    background: var(--primary-dark);
    box-shadow: var(--shadow-md);
}
```

#### 次要按钮
```css
.btn-secondary {
    background: var(--white);
    color: var(--gray-800);
    border: 1px solid var(--gray-200);
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--primary-blue);
}
```

#### 危险按钮
```css
.btn-danger {
    background: var(--risk-high);
    color: var(--white);
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-weight: 500;
    border: none;
}
```

### 卡片组件
```css
.card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 24px;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--gray-800);
}
```

### 导航组件
```css
.nav-item {
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--gray-800);
}

.nav-item:hover {
    background: var(--gray-100);
}

.nav-item.active {
    background: var(--primary-blue);
    color: var(--white);
}
```

### 表单组件
```css
.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(27, 75, 140, 0.1);
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--gray-800);
}
```

---

## 🎨 图标设计系统

### 图标风格
- **设计风格**: Material Design风格
- **图标类型**: SVG矢量图标
- **线条风格**: 简洁现代，统一粗细
- **填充方式**: currentColor自适应主题

### 尺寸规范
```css
/* 图标尺寸 */
--icon-xs: 12px;              /* 极小图标 */
--icon-sm: 16px;              /* 导航图标 */
--icon-md: 20px;              /* 标题图标 */
--icon-lg: 24px;              /* 功能图标 */
--icon-xl: 48px;              /* 大型图标 */

/* 使用场景 */
.nav-icon { width: 16px; height: 16px; }
.section-icon { width: 20px; height: 20px; }
.upload-icon { width: 48px; height: 48px; }
```

### 图标分类

#### 文档类图标
- 📄 合同文档: `document-text`
- 📋 列表模板: `clipboard-list`
- 📊 报告图表: `chart-bar`
- 💾 保存文档: `save`

#### 功能类图标
- 🔍 搜索对比: `search`
- ⚙️ 系统设置: `cog`
- 📤 导出功能: `upload`
- 🔄 刷新更新: `refresh`

#### 状态类图标
- ✅ 成功完成: `check-circle`
- ⚠️ 警告提示: `exclamation-triangle`
- ❌ 错误失败: `x-circle`
- 🔄 处理中: `loading`

---

## 🚨 状态与反馈设计

### 风险等级标注系统

#### 高风险标注
```css
.risk-highlight.high {
    background: rgba(229, 62, 62, 0.15);
    border-left: 3px solid var(--risk-high);
    padding: 2px 4px;
    border-radius: var(--radius-sm);
    cursor: pointer;
}
```

#### 中风险标注
```css
.risk-highlight.medium {
    background: rgba(246, 173, 85, 0.15);
    border-left: 3px solid var(--risk-medium);
    padding: 2px 4px;
    border-radius: var(--radius-sm);
    cursor: pointer;
}
```

#### 低风险标注
```css
.risk-highlight.low {
    background: rgba(56, 161, 105, 0.15);
    border-left: 3px solid var(--risk-low);
    padding: 2px 4px;
    border-radius: var(--radius-sm);
    cursor: pointer;
}
```

### 处理状态设计

#### 状态标签样式
```css
.status-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.status-processing {
    background: rgba(246, 173, 85, 0.1);
    color: var(--risk-medium);
}

.status-completed {
    background: rgba(56, 161, 105, 0.1);
    color: var(--risk-low);
}

.status-error {
    background: rgba(229, 62, 62, 0.1);
    color: var(--risk-high);
}
```

### 交互反馈规范

#### 悬停效果
```css
.interactive-element {
    transition: all 0.2s ease;
}

.interactive-element:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}
```

#### 加载状态
```css
.loading-state {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-blue);
    transition: width 0.3s ease;
}
```

---

## 📱 响应式设计规范

### 桌面端 (>1024px)
- 侧边栏宽度: 280px
- 内容区内边距: 24px
- 网格布局: 4列风险统计卡片
- 版本对比: 左右并排显示

### 平板端 (768px-1024px)
- 侧边栏宽度: 240px
- 内容区内边距: 20px
- 网格布局: 2列风险统计卡片
- 版本对比: 上下堆叠显示

### 移动端 (<768px)
- 侧边栏: 折叠为顶部导航
- 内容区内边距: 16px
- 网格布局: 1列显示
- 字体大小: 适当增大触控目标

---

## 💡 最佳实践

### 设计原则
1. **保持一致性**: 使用统一的颜色、字体、间距
2. **突出重点**: 用颜色和大小强调重要信息
3. **简化操作**: 减少用户的认知负担
4. **及时反馈**: 为用户操作提供明确反馈
5. **考虑可访问性**: 确保色彩对比度和字体大小

### 代码规范
```css
/* CSS变量使用 */
:root {
    /* 在根元素定义所有设计变量 */
}

/* 组件命名 */
.component-name { /* 使用语义化命名 */ }
.component-name__element { /* BEM命名规范 */ }
.component-name--modifier { /* 修饰符命名 */ }

/* 响应式设计 */
@media (max-width: 768px) {
    /* 移动端优先的响应式设计 */
}
```

### 设计检查清单
- [ ] 颜色符合品牌规范
- [ ] 字体大小和层级正确
- [ ] 间距使用标准单位
- [ ] 交互状态完整
- [ ] 响应式适配良好
- [ ] 可访问性达标

---

## 📚 附录

### 设计资源
- **图标库**: Material Design Icons
- **字体**: PingFang SC / SF Pro
- **设计工具**: Figma, Sketch
- **开发工具**: CSS Variables, Sass

### 更新记录
- **v1.0** (2024-08-06): 初始版本发布
- 后续版本将根据产品迭代持续更新

### 联系方式
如有设计规范相关问题，请联系设计团队进行确认和更新。

---

*本设计风格指南将随着产品发展持续完善，确保设计系统的一致性和可维护性。*