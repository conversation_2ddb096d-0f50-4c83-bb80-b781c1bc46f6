{"a-affix/offsetBottom": {"type": "number", "description": "Offset from the bottom of the viewport (in pixels), Default: -"}, "a-affix/offsetTop": {"type": "number", "description": "Offset from the top of the viewport (in pixels), Default: 0"}, "a-affix/target": {"type": "() => HTMLElement", "description": "Specifies the scrollable area DOM node, Default: () => window"}, "a-alert/action": {"type": "slot", "description": "The action of <PERSON><PERSON>, <PERSON><PERSON><PERSON>: -"}, "a-alert/afterClose": {"type": "() => void", "description": "Called when close animation is finished, De<PERSON>ult: -"}, "a-alert/banner": {"type": "boolean", "description": "Whether to show as banner, Default: false"}, "a-alert/closable": {"type": "boolean", "description": "Whether <PERSON><PERSON> can be closed, De<PERSON><PERSON>: "}, "a-alert/closeIcon": {"type": "slot", "description": "Custom close icon, Default: `<CloseOutlined />`"}, "a-alert/closeText": {"type": "string|slot", "description": "Close text to show, De<PERSON>ult: -"}, "a-alert/description": {"type": "string|slot", "description": "Additional content of <PERSON><PERSON>, <PERSON><PERSON><PERSON>: -"}, "a-alert/icon": {"type": "vnode | slot", "description": "Custom icon, effective when `showIcon` is `true`, Default: -"}, "a-alert/message": {"type": "string|slot", "description": "Content of <PERSON><PERSON>, De<PERSON><PERSON>: -"}, "a-alert/showIcon": {"type": "boolean", "description": "Whether to show icon, Default: false,in `banner` mode default is true"}, "a-alert/type": {"type": "string", "description": "Type of Alert styles, options: `success`, `info`, `warning`, `error`, Default: `info`,in `banner` mode default is `warning`"}, "a-anchor/affix": {"type": "boolean", "description": "Fixed mode of Anchor, De<PERSON>ult: true"}, "a-anchor/bounds": {"type": "number", "description": "Bounding distance of anchor area, Default: 5(px)"}, "a-anchor/getContainer": {"type": "() => HTMLElement", "description": "Scrolling container, Default: () => window"}, "a-anchor/getCurrentAnchor": {"type": "(activeLink: string) => string", "description": "Customize the anchor highlight, Default: -"}, "a-anchor/offsetBottom": {"type": "number", "description": "Pixels to offset from bottom when calculating position of scroll, Default: -"}, "a-anchor/offsetTop": {"type": "number", "description": "Pixels to offset from top when calculating position of scroll, Default: 0"}, "a-anchor/showInkInFixed": {"type": "boolean", "description": "Whether show ink-square when `：affix=\"false\"`, Default: false"}, "a-anchor/targetOffset": {"type": "number", "description": "Anchor scroll offset, default as `offsetTop`, [example](#components-anchor-demo-targetoffset), Default: `offsetTop`"}, "a-anchor/wrapperClass": {"type": "string", "description": "The class name of the container, De<PERSON>ult: -"}, "a-anchor/wrapperStyle": {"type": "object", "description": "The style of the container, Default: -"}, "a-anchor/items": {"type": "{ key, href, title, target, children }[] [see](#anchoritem)", "description": "Data configuration option content, support nesting through children, Default: -"}, "a-anchor/direction": {"type": "`vertical` | `horizontal`", "description": "Set Anchor direction, De<PERSON>ult: `vertical`"}, "a-anchor/customTitle": {"type": "v-slot=\"AnchorItem\"", "description": "custom option title by slot, Default: -"}, "a-anchor-item/key": {"type": "string | number", "description": "The unique identifier of the Anchor Link, Default: -"}, "a-anchor-item/href": {"type": "string", "description": "The target of hyperlink, <PERSON><PERSON><PERSON>: "}, "a-anchor-item/target": {"type": "string", "description": "Specifies where to display the linked URL, De<PERSON><PERSON>: "}, "a-anchor-item/title": {"type": "VueNode | (item: AnchorItem) => VueNode", "description": "The content of hyperlink, De<PERSON><PERSON>: "}, "a-anchor-item/children": {"type": "[AnchorItem](#anchoritem)[]", "description": "Nested Anchor Link, `Attention: This attribute does not support horizontal orientation`, Default: -"}, "a-app/message": {"type": "[MessageConfig](/components/message/#messageconfig)", "description": "Global config for Message, De<PERSON><PERSON>: -"}, "a-app/notification": {"type": "[NotificationConfig](/components/notification/#notificationconfig)", "description": "Global config for Notification, Default: -"}, "a-auto-complete/allowClear": {"type": "boolean", "description": "Show clear button, effective in multiple mode only., Default: false"}, "a-auto-complete/autofocus": {"type": "boolean", "description": "get focus when component mounted, Default: false"}, "a-auto-complete/backfill": {"type": "boolean", "description": "backfill selected item the input when using keyboard, Default: false"}, "a-auto-complete/bordered": {"type": "boolean", "description": "Whether has border style, Default: true"}, "a-auto-complete/clearIcon": {"type": "slot", "description": "Use slot custom clear icon, Default: `<CloseCircleFilled />`"}, "a-auto-complete/default (for customize input element)": {"type": "slot", "description": "customize input element, Default: `<Input />`"}, "a-auto-complete/defaultActiveFirstOption": {"type": "boolean", "description": "Whether active first option by default, Default: true"}, "a-auto-complete/defaultOpen": {"type": "boolean", "description": "Initial open state of dropdown, De<PERSON>ult: -"}, "a-auto-complete/disabled": {"type": "boolean", "description": "Whether disabled select, Default: false"}, "a-auto-complete/popupClassName": {"type": "string", "description": "The className of dropdown menu, Default: -"}, "a-auto-complete/dropdownMatchSelectWidth": {"type": "boolean | number", "description": "Determine whether the dropdown menu and the select input are the same width. De<PERSON><PERSON> set `min-width` same as input. Will ignore when value less than select width. `false` will disable virtual scroll, Default: true"}, "a-auto-complete/dropdownMenuStyle": {"type": "object", "description": "additional style applied to dropdown menu, Default: "}, "a-auto-complete/filterOption": {"type": "boolean or function(inputValue, option)", "description": "If true, filter options by input, if function, filter options against it. The function will receive two arguments, `inputValue` and `option`, if the function returns `true`, the option will be included in the filtered set; Otherwise, it will be excluded., Default: true"}, "a-auto-complete/open": {"type": "boolean", "description": "Controlled open state of dropdown, Default: -"}, "a-auto-complete/option": {"type": "v-slot:option=\"{value, label, [disabled, key, title]}\"", "description": "custom render option by slot, Default: -"}, "a-auto-complete/options": {"type": "[DataSourceItemType](https://github.com/vueComponent/ant-design-vue/blob/724d53b907e577cf5880c1e6742d4c3f924f8f49/components/auto-complete/index.vue#L9)[]", "description": "Data source for autocomplete, De<PERSON>ult: "}, "a-auto-complete/placeholder": {"type": "string", "description": "placeholder of input, Default: -"}, "a-auto-complete/status": {"type": "'error' | 'warning'", "description": "Set validation status, Default: -"}, "a-auto-complete/v-model:value": {"type": "string|string[]|{ key: string, label: string|vNodes }|Array&lt;{ key: string, label: string|vNodes }>", "description": "selected option, Default: -"}, "a-avatar/alt": {"type": "string", "description": "This attribute defines the alternative text describing the image, Default: -"}, "a-avatar/crossOrigin": {"type": "`'anonymous'` | `'use-credentials'` | `''`", "description": "cors settings attributes, Default: -"}, "a-avatar/draggable": {"type": "boolean | `'true'` | `'false'`", "description": "Whether the picture is allowed to be dragged, Default: -"}, "a-avatar/gap": {"type": "number", "description": "Letter type unit distance between left and right sides, Default: 4"}, "a-avatar/icon": {"type": "VNode | slot", "description": "the `Icon` type for an icon avatar, see `Icon` Component, Default: -"}, "a-avatar/loadError": {"type": "() => boolean", "description": "handler when img load error, return false to prevent default fallback behavior, Default: -"}, "a-avatar/shape": {"type": "`circle` | `square`", "description": "the shape of avatar, Default: `circle`"}, "a-avatar/size": {"type": "number | `large` | `small` | `default` | { xs: number, sm: number, ...}", "description": "The size of the avatar, Default: `default`"}, "a-avatar/src": {"type": "string", "description": "the address of the image for an image avatar, Default: -"}, "a-avatar/srcset": {"type": "string", "description": "a list of sources to use for different screen resolutions, Default: -"}, "a-avatar-group/maxCount": {"type": "number", "description": "Max avatars to show, De<PERSON>ult: -"}, "a-avatar-group/maxPopoverPlacement": {"type": "`top` | `bottom`", "description": "The placement of excess avatar Pop<PERSON>, Default: `top`"}, "a-avatar-group/maxPopoverTrigger": {"type": "`hover` | `focus` | `click`", "description": "Set the trigger of excess avatar <PERSON><PERSON>, De<PERSON>ult: `hover`"}, "a-avatar-group/maxStyle": {"type": "CSSProperties", "description": "The style of excess avatar style, Default: -"}, "a-avatar-group/size": {"type": "number | `large` | `small` | `default` | { xs: number, sm: number, ...}", "description": "The size of the avatar, Default: `default`"}, "a-avatar-group/shape": {"type": "`circle` | `square`", "description": "The shape of the avatar, Default: `circle`"}, "a-breadcrumb/itemRender": {"type": "({route, params, routes, paths}) => vNode", "description": "Custom item renderer, #itemRender=\"{route, params, routes, paths}\", Default: "}, "a-breadcrumb/params": {"type": "object", "description": "Routing parameters, Default: "}, "a-breadcrumb/routes": {"type": "[routes[]](#routes)", "description": "The routing stack information of router, De<PERSON>ult: "}, "a-breadcrumb/separator": {"type": "string|slot", "description": "Custom separator, Default: "}, "a-breadcrumb-item/href": {"type": "string", "description": "Target of hyperlink, De<PERSON>ult: -"}, "a-breadcrumb-item/overlay": {"type": "[Menu](/components/menu) | () => Menu", "description": "The dropdown menu, Default: -"}, "a-button/block": {"type": "boolean", "description": "option to fit button width to its parent width, Default: `false`"}, "a-button/danger": {"type": "boolean", "description": "set the danger status of button, De<PERSON>ult: `false`"}, "a-button/disabled": {"type": "boolean", "description": "disabled state of button, Default: `false`"}, "a-button/ghost": {"type": "boolean", "description": "make background transparent and invert text and border colors, Default: `false`"}, "a-button/href": {"type": "string", "description": "redirect url of link button, Default: -"}, "a-button/htmlType": {"type": "string", "description": "set the original html `type` of `button`, see: [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/button#attr-type), Default: `button`"}, "a-button/icon": {"type": "v-slot", "description": "set the icon of button, see: Icon component, Default: -"}, "a-button/loading": {"type": "boolean | { delay: number }", "description": "set the loading status of button, De<PERSON>ult: `false`"}, "a-button/shape": {"type": "`default` | `circle` | `round`", "description": "Can be set button shape, Default: `default`"}, "a-button/size": {"type": "`large` | `middle` | `small`", "description": "set the size of button, De<PERSON>ult: `middle`"}, "a-button/target": {"type": "string", "description": "same as target attribute of a, works when href is specified, Default: -"}, "a-button/type": {"type": "`primary` | `ghost` | `dashed` | `link` | `text` | `default`", "description": "can be set button type, Default: `default`"}, "a-calendar/dateCellRender": {"type": "v-slot:dateCellRender=\"{current: dayjs}\"", "description": "Customize the display of the date cell by setting a scoped slot, the returned content will be appended to the cell, Default: -"}, "a-calendar/dateFullCellRender": {"type": "v-slot:dateFullCellRender=\"{current: dayjs}\"", "description": "Customize the display of the date cell by setting a scoped slot, the returned content will override the cell, Default: -"}, "a-calendar/disabledDate": {"type": "(currentDate: dayjs) => boolean", "description": "Function that specifies the dates that cannot be selected, Default: -"}, "a-calendar/fullscreen": {"type": "boolean", "description": "Whether to display in full-screen, De<PERSON>ult: `true`"}, "a-calendar/headerRender": {"type": "v-slot:headerRender=\"{value: dayjs, type: string, onChange: f(), onTypeChange: f()}\"", "description": "render custom header in panel, Default: -"}, "a-calendar/locale": {"type": "object", "description": "The calendar's locale, Default: [default](https://github.com/vueComponent/ant-design-vue/blob/main/components/date-picker/locale/example.json)"}, "a-calendar/mode": {"type": "`month` | `year`", "description": "The display mode of the calendar, Default: `month`"}, "a-calendar/monthCellRender": {"type": "v-slot:monthCellRender=\"{current: dayjs}\"", "description": "Customize the display of the month cell by setting a scoped slot, the returned content will be appended to the cell, Default: -"}, "a-calendar/monthFullCellRender": {"type": "v-slot:monthFull<PERSON>ellRender=\"{current: dayjs}\"", "description": "Customize the display of the month cell by setting a scoped slot, the returned content will override the cell, Default: -"}, "a-calendar/validRange": {"type": "[[dayjs](https://day.js.org/), [dayjs](https://day.js.org/)]", "description": "to set valid range, Default: -"}, "a-calendar/value(v-model)": {"type": "[dayjs](https://day.js.org/)", "description": "The current selected date, Default: current date"}, "a-calendar/valueFormat": {"type": "string, [date formats](https://day.js.org/docs/en/display/format)", "description": "optional, format of binding value. If not specified, the binding value will be a Date object, Default: -"}, "a-card/activeTabKey": {"type": "string", "description": "Current TabPane's key, Default: -"}, "a-card/bodyStyle": {"type": "object", "description": "Inline style to apply to the card content, Default: -"}, "a-card/bordered": {"type": "boolean", "description": "Toggles rendering of the border around the card, De<PERSON>ult: `true`"}, "a-card/defaultActiveTabKey": {"type": "string", "description": "Initial active TabPane's key, if `activeTabKey` is not set., Default: -"}, "a-card/extra": {"type": "-", "description": "Content to render in the top-right corner of the card, De<PERSON><PERSON>: "}, "a-card/headStyle": {"type": "object", "description": "Inline style to apply to the card head, Default: -"}, "a-card/hoverable": {"type": "boolean", "description": "Lift up when hovering card, De<PERSON>ult: false"}, "a-card/loading": {"type": "boolean", "description": "Shows a loading indicator while the contents of the card are being fetched, Default: false"}, "a-card/size": {"type": "`default` | `small`", "description": "Size of card, Default: `default`"}, "a-card/tabList": {"type": "Array&lt;{key: string, tab: any}>", "description": "List of <PERSON>bP<PERSON>'s head, Custom tabs with the customTab(v3.0) slot, Default: -"}, "a-card/title": {"type": "-", "description": "Card title, Default: "}, "a-card/type": {"type": "string", "description": "Card style type, can be set to `inner` or not set, Default: -"}, "a-card/actions": {"type": "-", "description": "The action list, shows at the bottom of the Card., Default: "}, "a-card/cover": {"type": "-", "description": "Card cover, Default: "}, "a-card/customTab": {"type": "{ item: tabList[number] }", "description": "custom tabList tab, Default: "}, "a-card/tabBarExtraContent": {"type": "-", "description": "Extra content in tab bar, Default: "}, "a-card-meta/avatar": {"type": "slot", "description": "avatar or icon, Default: -"}, "a-card-meta/description": {"type": "string|slot", "description": "description content, Default: -"}, "a-card-meta/title": {"type": "string|slot", "description": "title content, Default: -"}, "a-carousel/autoplay": {"type": "boolean", "description": "Whether to scroll automatically, Default: `false`"}, "a-carousel/dotPosition": {"type": "string", "description": "The position of the dots, which can be one of `top` `bottom` `left` `right`, Default: `bottom`"}, "a-carousel/dots": {"type": "boolean", "description": "Whether to show the dots at the bottom of the gallery, De<PERSON>ult: `true`"}, "a-carousel/dotsClass": {"type": "string", "description": "Class name of the dots, Default: `slick-dots`"}, "a-carousel/easing": {"type": "string", "description": "Transition interpolation function name, Default: `linear`"}, "a-carousel/effect": {"type": "`scrollx` | `fade`", "description": "Transition effect, Default: `scrollx`"}, "a-carousel/afterChange": {"type": "function(current)", "description": "Callback function called after the current index changes, Default: -"}, "a-carousel/beforeChange": {"type": "function(from, to)", "description": "Callback function called before the current index changes, Default: -"}, "a-cascader/allowClear": {"type": "boolean", "description": "whether allow clear, Default: true"}, "a-cascader/autofocus": {"type": "boolean", "description": "get focus when component mounted, Default: false"}, "a-cascader/bordered": {"type": "boolean", "description": "Whether has border style, Default: true"}, "a-cascader/clearIcon": {"type": "slot", "description": "The custom clear icon, Default: -"}, "a-cascader/changeOnSelect": {"type": "boolean", "description": "(Work on single select) change value on each selection if set to true, see above demo for details, Default: false"}, "a-cascader/disabled": {"type": "boolean", "description": "whether disabled select, Default: false"}, "a-cascader/displayRender": {"type": "`({labels, selectedOptions}) => VNode`", "description": "render function of displaying selected options, you can use #displayRender=\"{labels, selectedOptions}\"., Default: `labels => labels.join(' / ')`"}, "a-cascader/popupClassName": {"type": "string", "description": "additional className of popup overlay, Default: -"}, "a-cascader/dropdownStyle": {"type": "CSSProperties", "description": "additional style of popup overlay, Default: {}"}, "a-cascader/expandIcon": {"type": "slot", "description": "Customize the current item expand icon, Default: -"}, "a-cascader/expandTrigger": {"type": "`click` | `hover`", "description": "expand current item when click or hover, Default: 'click'"}, "a-cascader/fieldNames": {"type": "object", "description": "custom field name for label and value and children, Default: `{ label: 'label', value: 'value', children: 'children' }`"}, "a-cascader/getPopupContainer": {"type": "Function(triggerNode)", "description": "Parent Node which the selector should be rendered to. Default to `body`. When position issues happen, try to modify it into scrollable content and position it relative., Default: () => document.body"}, "a-cascader/loadData": {"type": "`(selectedOptions) => void`", "description": "To load option lazily, and it cannot work with `showSearch`, Default: -"}, "a-cascader/maxTagCount": {"type": "number | `responsive`", "description": "Max tag count to show. `responsive` will cost render performance, Default: -"}, "a-cascader/maxTagPlaceholder": {"type": "v-slot | function(omittedValues)", "description": "Placeholder for not showing tags, Default: -"}, "a-cascader/multiple": {"type": "boolean", "description": "Support multiple or not, Default: -"}, "a-cascader/notFoundContent": {"type": "string | slot", "description": "Specify content to show when no result matches., De<PERSON>ult: 'Not Found'"}, "a-cascader/open": {"type": "boolean", "description": "set visible of cascader popup, De<PERSON>ult: -"}, "a-cascader/options": {"type": "[Option](#option)[]", "description": "data options of cascade, Default: -"}, "a-cascader/placeholder": {"type": "string", "description": "input placeholder, De<PERSON>ult: 'Please select'"}, "a-cascader/placement": {"type": "`bottomLeft` | `bottomRight` | `topLeft` | `topRight`", "description": "Use preset popup align config from builtinPlacements, Default: `bottomLeft`"}, "a-cascader/removeIcon": {"type": "slot", "description": "The custom remove icon, Default: -"}, "a-cascader/searchValue": {"type": "string", "description": "Set search value, Need work with `showSearch`, Default: -"}, "a-cascader/showSearch": {"type": "boolean | [object](#showsearch)", "description": "Whether show search input in single mode., Default: false"}, "a-cascader/size": {"type": "`large` | `default` | `small`", "description": "input size, Default: `default`"}, "a-cascader/status": {"type": "'error' | 'warning'", "description": "Set validation status, Default: -"}, "a-cascader/suffixIcon": {"type": "string | VNode | slot", "description": "The custom suffix icon, Default: -"}, "a-cascader/showCheckedStrategy": {"type": "`Cascader.SHOW_PARENT` | `Cascader.SHOW_CHILD`", "description": "The way show selected item in box. ** `SHOW_CHILD`: ** just show child treeNode. **`Cascader.SHOW_PARENT`:** just show parent treeNode (when all child treeNode under the parent treeNode are checked), Default: `Cascader.SHOW_PARENT`"}, "a-cascader/tagRender": {"type": "slot", "description": "Customize tag render when `multiple`, Default: -"}, "a-cascader/value(v-model)": {"type": "string[] | number[]", "description": "selected value, Default: -"}, "a-checkbox/autofocus": {"type": "boolean", "description": "get focus when component mounted, Default: false"}, "a-checkbox/checked(v-model)": {"type": "boolean", "description": "Specifies whether the checkbox is selected., Default: false"}, "a-checkbox/disabled": {"type": "boolean", "description": "Disable all checkboxes, Default: false"}, "a-checkbox/indeterminate": {"type": "boolean", "description": "indeterminate checked state of checkbox, Default: false"}, "a-checkbox/value": {"type": "boolean | string | number", "description": "value of checkbox in CheckboxGroup, Default: -"}, "a-checkbox/name": {"type": "string", "description": "The `name` property of all `input[type=\"checkbox\"]` children, Default: -"}, "a-checkbox/options": {"type": "string[] | Array&lt;{ label: string value: string disabled?: boolean, indeterminate?: boolean, onChange?: function }>", "description": "Specifies options, you can customize `label` with slot = \"label\" slot-scope=\"option\", Default: \\[]"}, "a-checkbox/value(v-model)": {"type": "(boolean | string | number)[]", "description": "Used for setting the currently selected value., Default: \\[]"}, "a-checkbox/blur()": {"type": "", "description": "remove focus, Default: undefined"}, "a-checkbox/focus()": {"type": "", "description": "get focus, De<PERSON>ult: undefined"}, "a-collapse/accordion": {"type": "boolean", "description": "If `true`, `Collapse` renders as `Accordion`, Default: `false`"}, "a-collapse/activeKey(v-model)": {"type": "string[] | string <br> number[] | number", "description": "Key of the active panel, Default: No default value. In `accordion` mode, it's the key of the first panel."}, "a-collapse/bordered": {"type": "boolean", "description": "Toggles rendering of the border around the collapse block, De<PERSON>ult: `true`"}, "a-collapse/collapsible": {"type": "`header` | `icon` | `disabled`", "description": "Specify whether the panels of children be collapsible or the trigger area of collapsible, Default: -"}, "a-collapse/destroyInactivePanel": {"type": "boolean", "description": "Destroy Inactive Panel, Default: `false`"}, "a-collapse/expandIcon": {"type": "Function(props):VNode | v-slot:expandIcon=\"props\"", "description": "allow to customize collapse icon, De<PERSON>ult: "}, "a-collapse/expandIconPosition": {"type": "`start` | `end`", "description": "Set expand icon position, Default: -"}, "a-collapse/ghost": {"type": "boolean", "description": "Make the collapse borderless and its background transparent, Default: false"}, "a-collapse-panel/collapsible": {"type": "`header` | `disabled`", "description": "Specify whether the panel be collapsible or the trigger area of collapsible, Default: -"}, "a-collapse-panel/disabled": {"type": "boolean", "description": "If `true`, panel cannot be opened or closed, Default: `false`"}, "a-collapse-panel/extra": {"type": "VNode | slot", "description": "extra element in the corner, De<PERSON><PERSON>: -"}, "a-collapse-panel/forceRender": {"type": "boolean", "description": "Forced render of content on panel, instead of lazy rending after clicking on header, Default: `false`"}, "a-collapse-panel/header": {"type": "string | slot", "description": "Title of the panel, De<PERSON>ult: -"}, "a-collapse-panel/key": {"type": "string | number", "description": "Unique key identifying the panel from among its siblings, <PERSON><PERSON><PERSON>: -"}, "a-collapse-panel/showArrow": {"type": "boolean", "description": "If `false`, panel will not show arrow icon, Default: `true`"}, "a-comment/actions": {"type": "Array | slot", "description": "List of action items rendered below the comment content, Default: -"}, "a-comment/author": {"type": "string|slot", "description": "The element to display as the comment author, De<PERSON><PERSON>: -"}, "a-comment/avatar": {"type": "string|slot", "description": "The element to display as the comment avatar - generally an antd `Avatar` or src, Default: -"}, "a-comment/content": {"type": "string|slot", "description": "The main content of the comment, De<PERSON><PERSON>: -"}, "a-comment/datetime": {"type": "string|slot", "description": "A datetime element containing the time to be displayed, Default: -"}, "a-config-provider/autoInsertSpaceInButton": {"type": "boolean", "description": "Set `false` to remove space between 2 chinese characters on Button, Default: true"}, "a-config-provider/componentSize": {"type": "`small` | `middle` | `large`", "description": "Config antd component size, Default: -"}, "a-config-provider/csp": {"type": "{ nonce: string }", "description": "Set [Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP) config, Default: -"}, "a-config-provider/direction": {"type": "`ltr` | `rtl`", "description": "Set direction of layout. See [demo](#components-config-provider-demo-direction), Default: `ltr`"}, "a-config-provider/dropdownMatchSelectWidth": {"type": "boolean | number", "description": "Determine whether the dropdown menu and the select input are the same width. De<PERSON>ult set `min-width` same as input. Will ignore when value less than select width. `false` will disable virtual scroll, Default: -"}, "a-config-provider/form": {"type": "{ validateMessages?: [ValidateMessages](/components/form/#validatemessages), requiredMark?: boolean | `optional` }", "description": "Set Form common props, Default: -"}, "a-config-provider/getPopupContainer": {"type": "Function(triggerNode, dialogContext)", "description": "to set the container of the popup element. The default is to create a `div` element in `body`., Default: `() => document.body`"}, "a-config-provider/getTargetContainer": {"type": "() => HTMLElement", "description": "Config Affix, Anchor scroll target container, Default: () => window"}, "a-config-provider/input": {"type": "{ autocomplete?: string }", "description": "Set Input common props, De<PERSON>ult: -"}, "a-config-provider/locale": {"type": "object", "description": "language package setting, you can find the packages in [ant-design-vue/es/locale](http://unpkg.com/ant-design-vue/es/locale/), Default: -"}, "a-config-provider/pageHeader": {"type": "{ ghost:boolean }", "description": "Unify the ghost of pageHeader ,Ref [pageHeader]\\(&lt;(/components/page-header)>, Default: 'true'"}, "a-config-provider/prefixCls": {"type": "string", "description": "set prefix class, Default: ant"}, "a-config-provider/renderEmpty": {"type": "slot-scope | Function(componentName: string): VNode", "description": "set empty content of components. Ref [Empty](/components/empty/), Default: -"}, "a-config-provider/space": {"type": "{ size: `small` | `middle` | `large` | `number` }", "description": "Set Space `size`, ref [Space](/components/space), Default: -"}, "a-config-provider/transformCellText": {"type": "Function({ text, column, record, index }) => any", "description": "Table data can be changed again before rendering. The default configuration of general user empty data., Default: -"}, "a-config-provider/virtual": {"type": "boolean", "description": "Disable virtual scroll when set to false, Default: true"}, "a-config-provider/wave": {"type": "{ disabled?: boolean }", "description": "Config wave effect, Default: -"}, "a-date-picker/allowClear": {"type": "boolean", "description": "Whether to show clear button, De<PERSON>ult: true"}, "a-date-picker/autofocus": {"type": "boolean", "description": "If get focus when component mounted, Default: false"}, "a-date-picker/bordered": {"type": "boolean", "description": "Whether has border style, Default: true"}, "a-date-picker/dateRender": {"type": "v-slot:dateRender=\"{current, today}\"", "description": "Custom rendering function for date cells, Default: -"}, "a-date-picker/disabled": {"type": "boolean", "description": "Determine whether the DatePicker is disabled, Default: false"}, "a-date-picker/disabledDate": {"type": "(currentDate: dayjs) => boolean", "description": "Specify the date that cannot be selected, Default: -"}, "a-date-picker/format": {"type": "[formatType](#formattype)", "description": "To set the date format, refer to [dayjs](https://day.js.org/). When an array is provided, all values are used for parsing and first value is used for formatting, support [Custom Format](#components-date-picker-demo-format), Default: `YYYY-MM-DD`"}, "a-date-picker/dropdownClassName": {"type": "string", "description": "To customize the className of the popup calendar, Default: -"}, "a-date-picker/getPopupContainer": {"type": "function(trigger)", "description": "To set the container of the floating layer, while the default is to create a `div` element in `body`, Default: -"}, "a-date-picker/inputReadOnly": {"type": "boolean", "description": "Set the `readonly` attribute of the input tag (avoids virtual keyboard on touch devices), Default: false"}, "a-date-picker/locale": {"type": "object", "description": "Localization configuration, Default: [default](https://github.com/vueComponent/ant-design-vue/blob/main/components/date-picker/locale/example.json)"}, "a-date-picker/mode": {"type": "`time` | `date` | `month` | `year` | `decade`", "description": "The picker panel mode, Default: -"}, "a-date-picker/nextIcon": {"type": "slot", "description": "The custom next icon, De<PERSON>ult: -"}, "a-date-picker/open": {"type": "boolean", "description": "The open state of picker, De<PERSON><PERSON>: -"}, "a-date-picker/picker": {"type": "`date` | `week` | `month` | `quarter` | `year`", "description": "Set picker type, Default: `date`"}, "a-date-picker/placeholder": {"type": "string | [string,string]", "description": "The placeholder of date input, Default: -"}, "a-date-picker/placement": {"type": "`bottomLeft` `bottomRight` `topLeft` `topRight`", "description": "The position where the selection box pops up, Default: bottomLeft"}, "a-date-picker/popupStyle": {"type": "CSSProperties", "description": "To customize the style of the popup calendar, Default: {}"}, "a-date-picker/presets": {"type": "{ label: slot, value: [dayjs](https://day.js.org/) }[]", "description": "The preset ranges for quick selection, Default: -"}, "a-date-picker/prevIcon": {"type": "slot", "description": "The custom prev icon, Default: -"}, "a-date-picker/size": {"type": "`large` | `middle` | `small`", "description": "To determine the size of the input box, the height of `large` and `small`, are 40px and 24px respectively, while default size is 32px, Default: -"}, "a-date-picker/status": {"type": "'error' | 'warning'", "description": "Set validation status, Default: -"}, "a-date-picker/suffixIcon": {"type": "v-slot:suffixIcon", "description": "The custom suffix icon, Default: -"}, "a-date-picker/superNextIcon": {"type": "slot", "description": "The custom super next icon, De<PERSON><PERSON>: -"}, "a-date-picker/superPrevIcon": {"type": "slot", "description": "The custom super prev icon, Default: -"}, "a-date-picker/valueFormat": {"type": "string, [date formats](https://day.js.org/docs/en/display/format)", "description": "optional, format of binding value. If not specified, the binding value will be a Date object, Default: -"}, "a-descriptions/bordered": {"type": "boolean", "description": "whether to display the border, Default: false"}, "a-descriptions/colon": {"type": "boolean", "description": "change default props `colon` value of `Descriptions.Item`, Default: true"}, "a-descriptions/column": {"type": "number", "description": "the number of `DescriptionItems` in a row,could be a number or a object like `{ xs: 8, sm: 16, md: 24}`,(Only set `bordered={true}` to take effect), Default: 3"}, "a-descriptions/contentStyle": {"type": "CSSProperties", "description": "Customize content style, Default: -"}, "a-descriptions/extra": {"type": "string | VNode | slot", "description": "The action area of the description list, placed at the top-right, Default: -"}, "a-descriptions/labelStyle": {"type": "CSSProperties", "description": "Customize label style, Default: -"}, "a-descriptions/layout": {"type": "`horizontal` | `vertical`", "description": "Define description layout, Default: `horizontal`"}, "a-descriptions/size": {"type": "`default` | `middle` | `small`", "description": "set the size of the list. Can be set to `middle`,`small`, or not filled, Default: `default`"}, "a-descriptions/title": {"type": "string | VNode | slot", "description": "The title of the description list, placed at the top, Default: -"}, "a-divider/dashed": {"type": "boolean", "description": "whether line is dashed, Default: false"}, "a-divider/orientation": {"type": "`left` | `right` | `center`", "description": "position of title inside divider, De<PERSON>ult: `center`"}, "a-divider/orientationMargin": {"type": "string | number", "description": "The margin-left/right between the title and its closest border, while the `orientation` must be `left` or `right`, Default: -"}, "a-divider/plain": {"type": "boolean", "description": "Divider text show as plain style, Default: true"}, "a-divider/type": {"type": "`horizontal` | `vertical`", "description": "direction type of divider, Default: `horizontal`"}, "a-drawer/autofocus": {"type": "boolean", "description": "Whether Drawer should get focused after open, Default: true"}, "a-drawer/bodyStyle": {"type": "CSSProperties", "description": "Style of the drawer content part, De<PERSON>ult: -"}, "a-drawer/class": {"type": "string", "description": "Config Drawer Panel className. Use `rootClassName` if want to config top dom style, Default: -"}, "a-drawer/closable": {"type": "boolean", "description": "Whether a close (x) button is visible on top left of the Drawer dialog or not, Default: true"}, "a-drawer/closeIcon": {"type": "VNode | slot", "description": "Custom close icon, Default: `<CloseOutlined />`"}, "a-drawer/contentWrapperStyle": {"type": "CSSProperties", "description": "Style of the drawer wrapper of content part, Default: -"}, "a-drawer/destroyOnClose": {"type": "boolean", "description": "Whether to unmount child components on closing drawer or not, Default: false"}, "a-drawer/extra": {"type": "VNode | slot", "description": "Extra actions area at corner, Default: -"}, "a-drawer/footer": {"type": "VNode | slot", "description": "The footer for Drawer, De<PERSON><PERSON>: -"}, "a-drawer/footerStyle": {"type": "CSSProperties", "description": "Style of the drawer footer part, Default: -"}, "a-drawer/forceRender": {"type": "boolean", "description": "Prerender Drawer component forcely, Default: false"}, "a-drawer/getContainer": {"type": "HTMLElement | `() => HTMLElement` | Selectors", "description": "mounted node and display window for Drawer, De<PERSON>ult: 'body'"}, "a-drawer/headerStyle": {"type": "CSSProperties", "description": "Style of the drawer header part, Default: -"}, "a-drawer/height": {"type": "string | number", "description": "Placement is `top` or `bottom`, height of the Drawer dialog, Default: 378"}, "a-drawer/keyboard": {"type": "boolean", "description": "Whether support press esc to close, De<PERSON>ult: true"}, "a-drawer/mask": {"type": "Boolean", "description": "Whether to show mask or not, De<PERSON>ult: true"}, "a-drawer/maskClosable": {"type": "boolean", "description": "Clicking on the mask (area outside the Drawer) to close the Drawer or not, Default: true"}, "a-drawer/maskStyle": {"type": "CSSProperties", "description": "Style for Drawer's mask element, Default: {}"}, "a-drawer/placement": {"type": "'top' | 'right' | 'bottom' | 'left'", "description": "The placement of the Drawer, <PERSON><PERSON><PERSON>: 'right'"}, "a-drawer/push": {"type": "boolean | {distance: string | number}", "description": "Nested drawers push behavior, Default: { distance: 180 }"}, "a-drawer/rootClassName": {"type": "string", "description": "The class name of the container of the Drawer dialog, Default: -"}, "a-drawer/rootStyle": {"type": "CSSProperties", "description": "Style of wrapper element which **contains mask** compare to `style`, Default: -"}, "a-drawer/style": {"type": "CSSProperties", "description": "Style of wrapper element which contains mask compare to drawerStyle, Default: -"}, "a-drawer/size": {"type": "`default` | `large`", "description": "presetted size of drawer, default `378px` and large `736px`, Default: `default`"}, "a-drawer/title": {"type": "string | slot", "description": "The title for Drawer, De<PERSON><PERSON>: -"}, "a-drawer/open(v-model)": {"type": "boolean", "description": "Whether the Drawer dialog is visible or not, Default: -"}, "a-drawer/width": {"type": "string | number", "description": "Width of the Drawer dialog, De<PERSON><PERSON>: 378"}, "a-drawer/zIndex": {"type": "Number", "description": "The `z-index` of the Drawer, Default: 1000"}, "a-empty/description": {"type": "string | v-slot", "description": "Customize description, Default: -"}, "a-empty/image": {"type": "string | v-slot", "description": "Customize image. Will tread as image url when string provided, Default: false"}, "a-empty/imageStyle": {"type": "CSSProperties", "description": "style of image, Default: -"}, "a-dropdown/align": {"type": "Object", "description": "this value will be merged into placement's config, please refer to the settings [dom-align](https://github.com/yiminghe/dom-align), Default: -"}, "a-dropdown/arrow": {"type": "boolean | { pointAtCenter: boolean }", "description": "Whether the dropdown arrow should be open, Default: false"}, "a-dropdown/destroyPopupOnHide": {"type": "boolean", "description": "Whether destroy dropdown when hidden, Default: false"}, "a-dropdown/disabled": {"type": "boolean", "description": "whether the dropdown menu is disabled, Default: -"}, "a-dropdown/getPopupContainer": {"type": "Function(triggerNode)", "description": "to set the container of the dropdown menu. The default is to create a `div` element in `body`, you can reset it to the scrolling area and make a relative reposition. [example](https://codepen.io/afc163/pen/zEjNOy?editors=0010), Default: `() => document.body`"}, "a-dropdown/overlay(v-slot)": {"type": "[Menu](/components/menu)", "description": "the dropdown menu, Default: -"}, "a-dropdown/overlayClassName": {"type": "string", "description": "Class name of the dropdown root element, Default: -"}, "a-dropdown/overlayStyle": {"type": "object", "description": "Style of the dropdown root element, Default: -"}, "a-dropdown/placement": {"type": "String", "description": "placement of pop menu: `bottomLeft` `bottom` `bottomRight` `topLeft` `top` `topRight`, Default: `bottomLeft`"}, "a-dropdown/trigger": {"type": "Array&lt;`click`|`hover`|`contextmenu`>", "description": "the trigger mode which executes the drop-down action, hover doesn't work on mobile device, Default: `['hover']`"}, "a-dropdown/open(v-model)": {"type": "boolean", "description": "whether the dropdown menu is open, De<PERSON>ult: -"}, "a-dropdown-button/disabled": {"type": "boolean", "description": "whether the dropdown menu is disabled, Default: -"}, "a-dropdown-button/icon": {"type": "vNode | slot", "description": "<PERSON><PERSON> (appears on the right), De<PERSON><PERSON>: -"}, "a-dropdown-button/loading": {"type": "boolean | { delay: number }", "description": "Set the loading status of button, Default: false"}, "a-dropdown-button/overlay(v-slot)": {"type": "[Menu](/components/menu)", "description": "the dropdown menu, Default: -"}, "a-dropdown-button/placement": {"type": "String", "description": "placement of pop menu: `bottomLeft` `bottom` `bottomRight` `topLeft` `top` `topRight`, Default: `bottomLeft`"}, "a-dropdown-button/size": {"type": "string", "description": "size of the button, the same as [<PERSON><PERSON>](/components/button), Default: `default`"}, "a-dropdown-button/trigger": {"type": "Array&lt;`click`|`hover`|`contextmenu`>", "description": "the trigger mode which executes the drop-down action, Default: `['hover']`"}, "a-dropdown-button/type": {"type": "string", "description": "type of the button, the same as [<PERSON><PERSON>](/components/button), Default: `default`"}, "a-dropdown-button/open(v-model)": {"type": "boolean", "description": "whether the dropdown menu is open, De<PERSON>ult: -"}, "a-flex/vertical": {"type": "boolean", "description": "Is direction of the flex vertical, use `flex-direction: column`, Default: `false`"}, "a-flex/wrap": {"type": "reference [flex-wrap](https://developer.mozilla.org/en-US/docs/Web/CSS/flex-wrap)", "description": "Set whether the element is displayed in a single line or in multiple lines, Default: nowrap"}, "a-flex/justify": {"type": "reference [justify-content](https://developer.mozilla.org/en-US/docs/Web/CSS/justify-content)", "description": "Sets the alignment of elements in the direction of the main axis, Default: normal"}, "a-flex/align": {"type": "reference [align-items](https://developer.mozilla.org/en-US/docs/Web/CSS/align-items)", "description": "Sets the alignment of elements in the direction of the cross axis, Default: normal"}, "a-flex/flex": {"type": "reference [flex](https://developer.mozilla.org/en-US/docs/Web/CSS/flex)", "description": "flex CSS shorthand properties, Default: normal"}, "a-flex/gap": {"type": "`small` | `middle` | `large` | string | number", "description": "Sets the gap between grids, Default: -"}, "a-flex/component": {"type": "Component", "description": "custom element type, Default: `div`"}, "a-float-button/icon": {"type": "slot", "description": "Set the icon component of button, De<PERSON>ult: -"}, "a-float-button/description": {"type": "string | slot", "description": "Text and other, Default: -"}, "a-float-button/tooltip": {"type": "string | slot", "description": "The text shown in the tooltip, De<PERSON><PERSON>: "}, "a-float-button/type": {"type": "`default` | `primary`", "description": "Setting button type, Default: `default`"}, "a-float-button/shape": {"type": "`circle` | `square`", "description": "Setting button shape, Default: `circle`"}, "a-float-button/href": {"type": "string", "description": "The target of hyperlink, De<PERSON><PERSON>: -"}, "a-float-button/target": {"type": "string", "description": "Specifies where to display the linked URL, Default: -"}, "a-float-button/badge": {"type": "[BadgeProps](/components/badge#api)", "description": "Attach Badge to FloatButton. `status` and other props related are not supported., Default: -"}, "a-form/colon": {"type": "boolean", "description": "change default props colon value of Form.Item (only effective when prop layout is horizontal), Default: true"}, "a-form/disabled": {"type": "boolean", "description": "Set form component disable, only available for antdv components, Default: false"}, "a-form/hideRequiredMark": {"type": "Boolean", "description": "Hide required mark of all form items, Default: false"}, "a-form/labelAlign": {"type": "'left' | 'right'", "description": "text align of label of all items, De<PERSON>ult: 'right'"}, "a-form/labelCol": {"type": "[object](/components/grid/#col)", "description": "The layout of label. You can set `span` `offset` to something like `{span: 3, offset: 12}` or `sm: {span: 3, offset: 12}` same as with `<Col>`, Default: "}, "a-form/labelWrap": {"type": "boolean", "description": "whether label can be wrap, Default: false"}, "a-form/layout": {"type": "'horizontal'|'vertical'|'inline'", "description": "Define form layout, Default: 'horizontal'"}, "a-form/model": {"type": "object", "description": "data of form component, Default: "}, "a-form/name": {"type": "string", "description": "Form name. Will be the prefix of Field `id`, Default: -"}, "a-form/noStyle": {"type": "boolean", "description": "No style for `true`, used as a pure field control, Default: false"}, "a-form/rules": {"type": "object", "description": "validation rules of form, Default: "}, "a-form/scrollToFirstError": {"type": "boolean | [options](https://github.com/stipsan/scroll-into-view-if-needed/#options)", "description": "Auto scroll to first failed field when submit, Default: false"}, "a-form/validateOnRuleChange": {"type": "boolean", "description": "whether to trigger validation when the `rules` prop is changed, Default: true"}, "a-form/validateTrigger": {"type": "string | string[]", "description": "Config field validate trigger, Default: `change`"}, "a-form/wrapperCol": {"type": "[object](/components/grid/#col)", "description": "The layout for input controls, same as `labelCol`, Default: "}, "a-form-item/autoLink": {"type": "boolean", "description": "Whether to automatically associate form fields. In most cases, you can use automatic association. If the conditions for automatic association are not met, you can manually associate them. See the notes below., Default: true"}, "a-form-item/colon": {"type": "boolean", "description": "Used with `label`, whether to display `:` after label text., Default: true"}, "a-form-item/extra": {"type": "string|slot", "description": "The extra prompt message. It is similar to help. Usage example: to display error message and prompt message at the same time., Default: "}, "a-form-item/hasFeedback": {"type": "boolean", "description": "Used with `validateStatus`, this option specifies the validation status icon. Recommended to be used only with `Input`., Default: false"}, "a-form-item/help": {"type": "string|slot", "description": "The prompt message. If not provided, the prompt message will be generated by the validation rule., De<PERSON>ult: "}, "a-form-item/htmlFor": {"type": "string", "description": "Set sub label `htmlFor`., Default: "}, "a-form-item/label": {"type": "string|slot", "description": "Label text, Default: "}, "a-form-item/labelAlign": {"type": "'left' | 'right'", "description": "text align of label, De<PERSON>ult: 'right'"}, "a-form-item/labelCol": {"type": "[object](/components/grid/#col)", "description": "The layout of label. You can set `span` `offset` to something like `{span: 3, offset: 12}` or `sm: {span: 3, offset: 12}` same as with `<Col>`, Default: "}, "a-form-item/name": {"type": "[NamePath](#namepath)", "description": "a key of `model`. In the use of validate and resetFields method, the attribute is required, Default: "}, "a-form-item/required": {"type": "boolean", "description": "Whether provided or not, it will be generated by the validation rule., Default: false"}, "a-form-item/rules": {"type": "object | array", "description": "validation rules of form, Default: "}, "a-form-item/tooltip": {"type": "string | slot", "description": "Config tooltip info, Default: "}, "a-form-item/validateFirst": {"type": "boolean", "description": "Whether stop validate on first rule of error for this field., Default: false"}, "a-form-item/validateStatus": {"type": "string", "description": "The validation status. If not provided, it will be generated by validation rule. options: 'success' 'warning' 'error' 'validating', Default: "}, "a-form-item/validateTrigger": {"type": "string | string[]", "description": "When to validate the value of children node, Default: `change`"}, "a-form-item/wrapperCol": {"type": "[object](/components/grid/#col)", "description": "The layout for input controls, same as `labelCol`, Default: "}, "a-common/rotate": {"type": "number", "description": "Rotate by n degrees (not working in IE9), Default: -"}, "a-common/spin": {"type": "boolean", "description": "Rotate icon with animation, Default: false"}, "a-common/style": {"type": "CSSProperties", "description": "Style properties of icon, like `fontSize` and `color`, Default: -"}, "a-common/twoToneColor": {"type": "string (hex color)", "description": "Only supports the two-tone icon. Specify the primary color., Default: -"}, "a-custom/component": {"type": "ComponentType&lt;CustomIconComponentProps>", "description": "The component used for the root node., Default: -"}, "a-custom/rotate": {"type": "number", "description": "Rotate degrees (not working in IE9), Default: -"}, "a-custom/spin": {"type": "boolean", "description": "Rotate icon with animation, Default: false"}, "a-custom/style": {"type": "CSSProperties", "description": "The computed style of the `svg` element, Default: -"}, "a-custom/extraCommonProps": {"type": "`{ class, attrs, props, on, style }`", "description": "Define extra properties to the component, Default: {}"}, "a-custom/scriptUrl": {"type": "string", "description": "The URL generated by [iconfont.cn](http://iconfont.cn/) project., Default: -"}, "a-custom/class": {"type": "string", "description": "The computed class name of the `svg` element, Default: -"}, "a-custom/fill": {"type": "string", "description": "Define the color used to paint the `svg` element, Default: 'currentColor'"}, "a-custom/height": {"type": "string | number", "description": "The height of the `svg` element, Default: '1em'"}, "a-custom/width": {"type": "string | number", "description": "The width of the `svg` element, Default: '1em'"}, "a-image/alt": {"type": "string", "description": "Image description, Default: -"}, "a-image/fallback": {"type": "string", "description": "Load failure fault-tolerant src, Default: -"}, "a-image/height": {"type": "string | number", "description": "Image height, Default: -"}, "a-image/placeholder": {"type": "boolean | slot", "description": "Load placeholder, use default placeholder when set `true`, Default: -"}, "a-image/preview": {"type": "boolean | [previewType](#previewtype)", "description": "preview config, disabled when `false`, Default: true"}, "a-image/src": {"type": "string", "description": "Image path, Default: -"}, "a-image/previewMask": {"type": "false | function | slot", "description": "custom mask, Default: -"}, "a-image/width": {"type": "string | number", "description": "Image width, Default: -"}, "a-input/addonAfter": {"type": "string|slot", "description": "The label text displayed after (on the right side of) the input field., De<PERSON>ult: "}, "a-input/addonBefore": {"type": "string|slot", "description": "The label text displayed before (on the left side of) the input field., De<PERSON>ult: "}, "a-input/allowClear": {"type": "boolean", "description": "allow to remove input content with clear icon, Default: "}, "a-input/bordered": {"type": "boolean", "description": "Whether has border style, Default: true"}, "a-input/clearIcon": {"type": "slot", "description": "custom clear icon when allowClear, Default: `<CloseCircleFilled />`"}, "a-input/defaultValue": {"type": "string", "description": "The initial input content, Default: "}, "a-input/disabled": {"type": "boolean", "description": "Whether the input is disabled., Default: false"}, "a-input/id": {"type": "string", "description": "The ID for input, Default: "}, "a-input/maxlength": {"type": "number", "description": "max length, Default: "}, "a-input/prefix": {"type": "string|slot", "description": "The prefix icon for the Input., Default: "}, "a-input/showCount": {"type": "boolean", "description": "Whether show text count, Default: false"}, "a-input/status": {"type": "'error' | 'warning'", "description": "Set validation status, Default: -"}, "a-input/size": {"type": "string", "description": "The size of the input box. Note: in the context of a form, the `middle` size is used. Available: `large` `middle` `small`, Default: -"}, "a-input/suffix": {"type": "string|slot", "description": "The suffix icon for the Input., Default: "}, "a-input/type": {"type": "string", "description": "The type of input, see: [MDN](https://developer.mozilla.org/docs/Web/HTML/Element/input#Form_%3Cinput%3E_types)(use `<a-textarea />` instead of `type=\"textarea\"`), Default: `text`"}, "a-input/value(v-model)": {"type": "string", "description": "The input content value, Default: "}, "a-input/change": {"type": "function(e)", "description": "callback when user input, De<PERSON>ult: "}, "a-input/pressEnter": {"type": "function(e)", "description": "The callback function that is triggered when Enter key is pressed., Default: "}, "a-input-search/enterButton": {"type": "boolean|slot", "description": "to show an enter button after input. This prop is conflict with addon., Default: false"}, "a-input-search/loading": {"type": "boolean", "description": "Search box with loading., De<PERSON>ult: "}, "a-input-search/search": {"type": "function(value, event)", "description": "The callback function that is triggered when you click on the search-icon or press Enter key., De<PERSON>ult: "}, "a-input-group/compact": {"type": "boolean", "description": "Whether use compact style, Default: false"}, "a-input-group/size": {"type": "string", "description": "The size of `Input.Group` specifies the size of the included `Input` fields. Available: `large` `default` `small`, Default: `default`"}, "a-input-password/visible(v-model)": {"type": "boolean", "description": "password visibility, Default: false"}, "a-input-password/iconRender": {"type": "slot", "description": "Custom toggle button, Default: -"}, "a-input-password/visibilityToggle": {"type": "boolean", "description": "Whether show toggle button or control password visible, Default: true"}, "a-input-number/addonAfter": {"type": "slot", "description": "The label text displayed after (on the right side of) the input field, Default: -"}, "a-input-number/addonBefore": {"type": "slot", "description": "The label text displayed before (on the left side of) the input field, Default: -"}, "a-input-number/autofocus": {"type": "boolean", "description": "get focus when component mounted, Default: false"}, "a-input-number/bordered": {"type": "boolean", "description": "Whether has border style, Default: true"}, "a-input-number/controls": {"type": "boolean", "description": "Whether to show `+-` controls, Default: true"}, "a-input-number/decimalSeparator": {"type": "string", "description": "decimal separator, Default: -"}, "a-input-number/defaultValue": {"type": "number", "description": "initial value, Default: "}, "a-input-number/disabled": {"type": "boolean", "description": "disable the input, Default: false"}, "a-input-number/formatter": {"type": "function(value: number | string, info: { userTyping: boolean, input: string }): string", "description": "Specifies the format of the value presented, Default: -"}, "a-input-number/keyboard": {"type": "boolean", "description": "If enable keyboard behavior, Default: true"}, "a-input-number/max": {"type": "number", "description": "max value, Default: Infinity"}, "a-input-number/min": {"type": "number", "description": "min value, Default: -Infinity"}, "a-input-number/parser": {"type": "function( string): number", "description": "Specifies the value extracted from formatter, Default: -"}, "a-input-number/precision": {"type": "number", "description": "precision of input value, Default: -"}, "a-input-number/prefix": {"type": "slot", "description": "The prefix icon for the Input, Default: -"}, "a-input-number/size": {"type": "string", "description": "height of input box, Default: -"}, "a-input-number/status": {"type": "'error' | 'warning'", "description": "Set validation status, Default: -"}, "a-input-number/step": {"type": "number|string", "description": "The number to which the current value is increased or decreased. It can be an integer or decimal., Default: 1"}, "a-input-number/stringMode": {"type": "boolean", "description": "Set value as string to support high precision decimals. Will return string value by `change`, Default: false"}, "a-input-number/value(v-model)": {"type": "number", "description": "current value, Default: "}, "a-input-number/upIcon": {"type": "slot", "description": "custom up icon, Default: `<UpOutlined />`"}, "a-input-number/downIcon": {"type": "slot", "description": "custom up down, Default: `<DownOutlined />`"}, "a-layout/class": {"type": "string", "description": "container className, Default: -"}, "a-layout/hasSider": {"type": "boolean", "description": "whether contain Sider in children, don't have to assign it normally. Useful in ssr avoid style flickering, Default: -"}, "a-layout/style": {"type": "object|string", "description": "to customize the styles, Default: -"}, "a-layout-sider/breakpoint": {"type": "`xs` | `sm` | `md` | `lg` | `xl` | `xxl`", "description": "[breakpoints](/components/grid#api) of the responsive layout, Default: -"}, "a-layout-sider/class": {"type": "string", "description": "container className, Default: -"}, "a-layout-sider/collapsed(v-model)": {"type": "boolean", "description": "to set the current status, De<PERSON>ult: -"}, "a-layout-sider/collapsedWidth": {"type": "number", "description": "width of the collapsed sidebar, by setting to `0` a special trigger will appear, Default: 80"}, "a-layout-sider/collapsible": {"type": "boolean", "description": "whether can be collapsed, Default: false"}, "a-layout-sider/defaultCollapsed": {"type": "boolean", "description": "to set the initial status, Default: false"}, "a-layout-sider/reverseArrow": {"type": "boolean", "description": "reverse direction of arrow, for a sider that expands from the right, Default: false"}, "a-layout-sider/style": {"type": "object|string", "description": "to customize the styles, Default: -"}, "a-layout-sider/theme": {"type": "`light` | `dark`", "description": "color theme of the sidebar, De<PERSON><PERSON>: `dark`"}, "a-layout-sider/trigger": {"type": "string|slot", "description": "specify the customized trigger, set to null to hide the trigger, Default: -"}, "a-layout-sider/width": {"type": "number|string", "description": "width of the sidebar, Default: 200"}, "a-layout-sider/zeroWidthTriggerStyle": {"type": "object", "description": "to customize the styles of the special trigger that appears when `collapsedWidth` is 0, Default: -"}, "a-list/bordered": {"type": "boolean", "description": "Toggles rendering of the border around the list, Default: false"}, "a-list/dataSource": {"type": "any[]", "description": "dataSource array for list, Default: -"}, "a-list/footer": {"type": "string|slot", "description": "List footer renderer, De<PERSON>ult: -"}, "a-list/grid": {"type": "object", "description": "The grid type of list. You can set grid to something like {gutter: 16, column: 4}, Default: -"}, "a-list/header": {"type": "string|slot", "description": "List header renderer, Default: -"}, "a-list/itemLayout": {"type": "string", "description": "The layout of list, default is `horizontal`, If a vertical list is desired, set the itemLayout property to `vertical`, Default: -"}, "a-list/loading": {"type": "boolean|[object](https://www.antdv.com/components/spin/#api)", "description": "Shows a loading indicator while the contents of the list are being fetched, Default: false"}, "a-list/loadMore": {"type": "string|slot", "description": "Shows a load more content, Default: -"}, "a-list/locale": {"type": "object", "description": "i18n text including empty text, Default: emptyText: 'No Data' <br>"}, "a-list/pagination": {"type": "boolean | object", "description": "Pagination [config](https://www.antdv.com/components/pagination/#api), hide it by setting it to false, Default: false"}, "a-list/renderItem": {"type": "({item, index}) => vNode", "description": "Custom item renderer, #renderItem=\"{item, index}\", Default: "}, "a-list/rowKey": {"type": "string|Function(record):string", "description": "<PERSON><PERSON>'s unique key, could be a string or function that returns a string, Default: `key`"}, "a-list/split": {"type": "boolean", "description": "Toggles rendering of the split under the list item, Default: true"}, "a-list/column": {"type": "number oneOf [ 1, 2, 3, 4, 6, 8, 12, 24]", "description": "column of grid, Default: -"}, "a-list/gutter": {"type": "number", "description": "spacing between grid, De<PERSON>ult: 0"}, "a-list/size": {"type": "`default` | `middle` | `small`", "description": "Size of list, Default: `default`"}, "a-list/xxxl": {"type": "number", "description": "`≥2000px` column of grid, Default: -"}, "a-list/xs": {"type": "number", "description": "`<576px` column of grid, Default: -"}, "a-list/sm": {"type": "number", "description": "`≥576px` column of grid, Default: -"}, "a-list/md": {"type": "number", "description": "`≥768px` column of grid, Default: -"}, "a-list/lg": {"type": "number", "description": "`≥992px` column of grid, Default: -"}, "a-list/xl": {"type": "number", "description": "`≥1200px` column of grid, Default: -"}, "a-list/xxl": {"type": "number", "description": "`≥1600px` column of grid, Default: -"}, "a-list-item/actions": {"type": "vNode[] |slot", "description": "The actions content of list item. If `itemLayout` is `vertical`, shows the content on bottom, otherwise shows content on the far right., Default: -"}, "a-list-item/extra": {"type": "string|slot", "description": "The extra content of list item. If `itemLayout` is `vertical`, shows the content on right, otherwise shows content on the far right., Default: -"}, "a-list-item-meta/avatar": {"type": "slot", "description": "The avatar of list item, Default: -"}, "a-list-item-meta/description": {"type": "string|slot", "description": "The description of list item, Default: -"}, "a-list-item-meta/title": {"type": "string|slot", "description": "The title of list item, Default: -"}, "a-menu/forceSubMenuRender": {"type": "boolean", "description": "render submenu into DOM before it shows, Default: false"}, "a-menu/inlineCollapsed": {"type": "boolean", "description": "specifies the collapsed status when menu is inline mode, Default: -"}, "a-menu/inlineIndent": {"type": "number", "description": "indent px of inline menu item on each level, Default: 24"}, "a-menu/items": {"type": "[ItemType[]](#itemtype)", "description": "Menu item content, Default: -"}, "a-menu/mode": {"type": "`vertical` | `horizontal` | `inline`", "description": "type of the menu; `vertical`, `horizontal`, and `inline` modes are supported, Default: `vertical`"}, "a-menu/multiple": {"type": "boolean", "description": "Allow selection of multiple items, Default: false"}, "a-menu/openKeys(v-model)": {"type": "(string | number)[]", "description": "array with the keys of currently opened sub menus, De<PERSON>ult: "}, "a-menu/overflowedIndicator": {"type": "slot", "description": "Customized the ellipsis icon when menu is collapsed horizontally, Default: `<EllipsisOutlined />`"}, "a-menu/selectable": {"type": "boolean", "description": "allow selecting menu items, Default: true"}, "a-menu/selectedKeys(v-model)": {"type": "(string | number)[]", "description": "array with the keys of currently selected menu items, De<PERSON>ult: "}, "a-menu/style": {"type": "object", "description": "style of the root node, De<PERSON><PERSON>: "}, "a-menu/subMenuCloseDelay": {"type": "number", "description": "delay time to hide submenu when mouse leave, unit: second, Default: 0.1"}, "a-menu/subMenuOpenDelay": {"type": "number", "description": "delay time to show submenu when mouse enter, unit: second, Default: 0"}, "a-menu/theme": {"type": "`light` | `dark`", "description": "color theme of the menu, Default: `light`"}, "a-menu/triggerSubMenuAction": {"type": "`click` | `hover`", "description": "method of trigger submenu, De<PERSON>ult: `hover`"}, "a-menu/click": {"type": "function({ item, key, keyPath })", "description": "callback executed when a menu item is clicked, Default: undefined"}, "a-menu/deselect": {"type": "function({ item, key, selectedKeys })", "description": "callback executed when a menu item is deselected, only supported for multiple mode, Default: undefined"}, "a-menu/openChange": {"type": "function(openKeys: (string | number)[])", "description": "called when open/close sub menu, Default: undefined"}, "a-menu/select": {"type": "function({ item, key, selectedKeys })", "description": "callback executed when a menu item is selected, Default: undefined"}, "a-menu-item/disabled": {"type": "boolean", "description": "whether menu item is disabled or not, Default: false"}, "a-menu-item/key": {"type": "string | number", "description": "unique id of the menu item, De<PERSON>ult: "}, "a-menu-item/title": {"type": "string | slot", "description": "set display title for collapsed item, De<PERSON>ult: "}, "a-menu-item-type/danger": {"type": "boolean", "description": "Display the danger style, Default: false"}, "a-menu-item-type/disabled": {"type": "boolean", "description": "Whether menu item is disabled, Default: false"}, "a-menu-item-type/icon": {"type": "VueNode | (item: MenuItemType) => VNode", "description": "The icon of the menu item, De<PERSON><PERSON>: -"}, "a-menu-item-type/key": {"type": "string | number", "description": "Unique ID of the menu item, Default: -"}, "a-menu-item-type/label": {"type": "VueNode", "description": "Menu label, Default: -"}, "a-menu-item-type/title": {"type": "string", "description": "Set display title for collapsed item, Default: -"}, "a-sub-menu-type/children": {"type": "[ItemType[]](#itemtype)", "description": "Sub-menus or sub-menu items, Default: -"}, "a-sub-menu-type/disabled": {"type": "boolean", "description": "Whether sub-menu is disabled, Default: false"}, "a-sub-menu-type/icon": {"type": "VueNode | (item: SubMenuType) => VueNode", "description": "Icon of sub menu, Default: -"}, "a-sub-menu-type/key": {"type": "string | number", "description": "Unique ID of the sub-menu, Default: -"}, "a-sub-menu-type/label": {"type": "VueNode", "description": "Menu label, Default: -"}, "a-sub-menu-type/popupClassName": {"type": "string", "description": "Sub-menu class name, not working when `mode=\"inline\"`, Default: -"}, "a-sub-menu-type/popupOffset": {"type": "[number, number]", "description": "Sub-menu offset, not working when `mode=\"inline\"`, Default: -"}, "a-sub-menu-type/theme": {"type": "", "description": "Color theme of the SubMenu (inherits from <PERSON><PERSON> by default), Default: `light` | `dark`"}, "a-sub-menu-type/onTitleClick": {"type": "function({ key, domEvent })", "description": "Callback executed when the sub-menu title is clicked, Default: -"}, "a-menu-item-group-type/children": {"type": "[MenuItemType[]](#menuitemtype)", "description": "Sub-menu items, Default: -"}, "a-menu-item-group-type/label": {"type": "VueNode", "description": "The title of the group, Default: -"}, "a-menu-divider-type/dashed": {"type": "boolean", "description": "Whether line is dashed, Default: false"}, "a-menu-sub-menu/disabled": {"type": "boolean", "description": "whether sub menu is disabled or not, Default: false"}, "a-menu-sub-menu/expandIcon": {"type": "slot", "description": "Customized expandIcon, Default: arrow icon"}, "a-menu-sub-menu/key": {"type": "string | number", "description": "Unique ID of the sub menu, required, Default: "}, "a-menu-sub-menu/popupClassName": {"type": "string", "description": "Sub-menu class name, Default: "}, "a-menu-sub-menu/popupOffset": {"type": "[number, number]", "description": "Sub-menu offset, not working when `mode=\"inline\"`, Default: -"}, "a-menu-sub-menu/title": {"type": "string|slot", "description": "title of the sub menu, De<PERSON>ult: "}, "a-menu-sub-menu/titleClick": {"type": "function({ key, domEvent })", "description": "callback executed when the sub menu title is clicked, Default: undefined"}, "a-menu-item-group/children": {"type": "MenuItem[]", "description": "sub-menu items, Default: "}, "a-menu-item-group/title": {"type": "string|slot", "description": "title of the group, Default: "}, "a-menu-divider/dashed": {"type": "boolean", "description": "Whether line is dashed, Default: false"}, "a-message/content": {"type": "string| VNode | () => VNode", "description": "content of the message, Default: -"}, "a-message/duration": {"type": "number", "description": "time(seconds) before auto-dismiss, don't dismiss if set to 0, <PERSON><PERSON><PERSON>: 1.5"}, "a-message/onClose": {"type": "function", "description": "Specify a function that will be called when the message is closed, Default: -"}, "a-modal/afterClose": {"type": "function", "description": "Specify a function that will be called when modal is closed completely., Default: -"}, "a-modal/bodyStyle": {"type": "object", "description": "Body style for modal body element. Such as height, padding etc., Default: {}"}, "a-modal/cancelButtonProps": {"type": "[ButtonProps](/components/button/#api)", "description": "The cancel button props, Default: -"}, "a-modal/cancelText": {"type": "string|slot", "description": "Text of the Cancel button, De<PERSON><PERSON>: `<PERSON><PERSON>`"}, "a-modal/centered": {"type": "boolean", "description": "Centered <PERSON><PERSON>, De<PERSON>ult: `false`"}, "a-modal/closable": {"type": "boolean", "description": "Whether a close (x) button is visible on top right of the modal dialog or not, Default: true"}, "a-modal/closeIcon": {"type": "VNode | slot", "description": "custom close icon, Default: -"}, "a-modal/confirmLoading": {"type": "boolean", "description": "Whether to apply loading visual effect for OK button or not, Default: false"}, "a-modal/destroyOnClose": {"type": "boolean", "description": "Whether to unmount child components on onClose, Default: false"}, "a-modal/footer": {"type": "string|slot", "description": "Footer content, set as `:footer=\"null\"` when you don't need default buttons, Default: OK and Cancel buttons"}, "a-modal/forceRender": {"type": "boolean", "description": "Force render <PERSON><PERSON>, De<PERSON><PERSON>: false"}, "a-modal/getContainer": {"type": "(instance): HTMLElement", "description": "Return the mount node for Modal, Default: () => document.body"}, "a-modal/mask": {"type": "boolean", "description": "Whether show mask or not., De<PERSON>ult: true"}, "a-modal/maskClosable": {"type": "boolean", "description": "Whether to close the modal dialog when the mask (area outside the modal) is clicked, Default: true"}, "a-modal/maskStyle": {"type": "object", "description": "Style for modal's mask element., Default: {}"}, "a-modal/okButtonProps": {"type": "[ButtonProps](/components/button/#api)", "description": "The ok button props, Default: -"}, "a-modal/okText": {"type": "string|slot", "description": "Text of the OK button, De<PERSON><PERSON>: `OK`"}, "a-modal/okType": {"type": "string", "description": "Button `type` of the OK button, Default: `primary`"}, "a-modal/title": {"type": "string|slot", "description": "The modal dialog's title, Default: -"}, "a-modal/open(v-model)": {"type": "boolean", "description": "Whether the modal dialog is visible or not, Default: false"}, "a-modal/width": {"type": "string|number", "description": "Width of the modal dialog, Default: 520"}, "a-modal/wrapClassName": {"type": "string", "description": "The class name of the container of the modal dialog, Default: -"}, "a-modal/zIndex": {"type": "number", "description": "The `z-index` of the Modal, Default: 1000"}, "a-notification/bottom": {"type": "string", "description": "Distance from the bottom of the viewport, when `placement` is `bottomRight` or `bottomLeft` (unit: pixels)., Default: `24px`"}, "a-notification/btn": {"type": "VNode | () => VNode", "description": "Customized close button, Default: -"}, "a-notification/class": {"type": "string", "description": "Customized CSS class, Default: -"}, "a-notification/closeIcon": {"type": "VNode | () => VNode", "description": "custom close icon, Default: -"}, "a-notification/description": {"type": "string| VNode | () => VNode", "description": "The content of notification box (required), Default: -"}, "a-notification/duration": {"type": "number", "description": "Time in seconds before Notification is closed. When set to 0 or null, it will never be closed automatically, Default: 4.5"}, "a-notification/getContainer": {"type": "() => HTMLNode", "description": "Return the mount node for Notification, Default: () => document.body"}, "a-notification/icon": {"type": "VNode | () => VNode", "description": "Customized icon, Default: -"}, "a-notification/key": {"type": "string", "description": "The unique identifier of the Notification, Default: -"}, "a-notification/message": {"type": "string| VNode | () => VNode", "description": "The title of notification box (required), Default: -"}, "a-notification/placement": {"type": "string", "description": "Position of Notification, can be one of `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight`, Default: `topRight`"}, "a-notification/style": {"type": "Object | string", "description": "Customized inline style, Default: -"}, "a-notification/top": {"type": "string", "description": "Distance from the top of the viewport, when `placement` is `topRight` or `topLeft` (unit: pixels)., Default: `24px`"}, "a-notification/onClick": {"type": "Function", "description": "Specify a function that will be called when the notification is clicked, Default: -"}, "a-notification/onClose": {"type": "Function", "description": "Specify a function that will be called when the close button is clicked, Default: -"}, "a-page-header/avatar": {"type": "[avatar props](/components/avatar/)", "description": "<PERSON><PERSON> next to the title bar, De<PERSON>ult: -"}, "a-page-header/backIcon": {"type": "string|slot", "description": "custom back icon, if false the back icon will not be displayed, Default: `<ArrowLeft />`"}, "a-page-header/breadcrumb": {"type": "[breadcrumb](/components/breadcrumb/)", "description": "Breadcrumb configuration, Default: -"}, "a-page-header/extra": {"type": "string|slot", "description": "Operating area, at the end of the line of the title line, Default: -"}, "a-page-header/footer": {"type": "string|slot", "description": "<PERSON><PERSON><PERSON><PERSON>'s footer, generally used to render TabBar, Default: -"}, "a-page-header/ghost": {"type": "boolean", "description": "PageHeader type, will change background color, Default: true"}, "a-page-header/subTitle": {"type": "string|slot", "description": "custom subTitle text, Default: -"}, "a-page-header/tags": {"type": "[Tag](/components/tag/)[] | [Tag](/components/tag/)", "description": "Tag list next to title, De<PERSON>ult: -"}, "a-page-header/title": {"type": "string|slot", "description": "custom title text, Default: -"}, "a-pagination/current(v-model)": {"type": "number", "description": "current page number, Default: -"}, "a-pagination/defaultCurrent": {"type": "number", "description": "default initial page number, Default: 1"}, "a-pagination/defaultPageSize": {"type": "number", "description": "default number of data items per page, Default: 10"}, "a-pagination/disabled": {"type": "boolean", "description": "Disable pagination, Default: -"}, "a-pagination/hideOnSinglePage": {"type": "boolean", "description": "Whether to hide pager on single page, Default: false"}, "a-pagination/itemRender": {"type": "(page, type: 'page' | 'prev' | 'next', originalElement) => vNode | v-slot", "description": "to customize item innerHTML, Default: -"}, "a-pagination/pageSize(v-model)": {"type": "number", "description": "number of data items per page, Default: -"}, "a-pagination/pageSizeOptions": {"type": "string[] | number[]", "description": "specify the sizeChanger options, Default: \\['10', '20', '50', '100']"}, "a-pagination/responsive": {"type": "boolean", "description": "If `size` is not specified, `Pagination` would resize according to the width of the window, Default: -"}, "a-pagination/showLessItems": {"type": "boolean", "description": "Show less page items, Default: false"}, "a-pagination/showQuickJumper": {"type": "boolean", "description": "determine whether you can jump to pages directly, Default: false"}, "a-pagination/showSizeChanger": {"type": "boolean", "description": "Determine whether to show `pageSize` select, it will be true when `total > 50`, Default: -"}, "a-pagination/showTitle": {"type": "boolean", "description": "Show page item's title, Default: true"}, "a-pagination/showTotal": {"type": "Function(total, range)", "description": "to display the total number and range, Default: -"}, "a-pagination/simple": {"type": "boolean", "description": "whether to use simple mode, De<PERSON>ult: -"}, "a-pagination/size": {"type": "string", "description": "specify the size of `Pagination`, can be set to `small`, Default: \"\""}, "a-pagination/total": {"type": "number", "description": "total number of data items, Default: 0"}, "a-popconfirm/cancelButton": {"type": "slot", "description": "custom render cancel button, Default: -"}, "a-popconfirm/cancelButtonProps": {"type": "[ButtonProps](/components/button/#api)", "description": "The cancel button props, Default: -"}, "a-popconfirm/cancelText": {"type": "string|slot", "description": "text of the Cancel button, De<PERSON><PERSON>: `<PERSON><PERSON>`"}, "a-popconfirm/disabled": {"type": "boolean", "description": "is show popconfirm when click its childrenNode, Default: false"}, "a-popconfirm/icon": {"type": "vNode|slot", "description": "customize icon of confirmation, Default: &lt;Icon type=\"exclamation-circle\" />"}, "a-popconfirm/okButton": {"type": "slot", "description": "custom render confirm button, Default: -"}, "a-popconfirm/okButtonProps": {"type": "[ButtonProps](/components/button/#api)", "description": "The ok button props, Default: -"}, "a-popconfirm/okText": {"type": "string|slot", "description": "text of the Confirm button, De<PERSON>ult: `Confirm`"}, "a-popconfirm/okType": {"type": "string", "description": "Button `type` of the Confirm button, Default: `primary`"}, "a-popconfirm/showCancel": {"type": "boolean", "description": "Show cancel button, De<PERSON>ult: true"}, "a-popconfirm/title": {"type": "string|slot", "description": "title of the confirmation box, De<PERSON>ult: -"}, "a-popconfirm/description": {"type": "string|slot", "description": "The description of the confirmation box title, Default: -"}, "a-popconfirm/visible (v-model)": {"type": "boolean", "description": "hide or show, Default: -"}, "a-popover/content": {"type": "string|slot|vNode", "description": "Content of the card, De<PERSON>ult: -"}, "a-popover/title": {"type": "string|slot|VNode", "description": "Title of the card, Default: -"}, "a-progress/format": {"type": "function(percent, successPercent)", "description": "The template function of the content, Default: (percent) => percent + `%`"}, "a-progress/percent": {"type": "number", "description": "To set the completion percentage, De<PERSON><PERSON>: 0"}, "a-progress/showInfo": {"type": "boolean", "description": "Whether to display the progress value and the status icon, Default: true"}, "a-progress/status": {"type": "string", "description": "To set the status of the Progress, options: `success` `exception` `normal` `active`(line only), Default: -"}, "a-progress/strokeColor": {"type": "string", "description": "The color of progress bar, Default: -"}, "a-progress/strokeLinecap": {"type": "`round` | `butt` | `square`, see [stroke-linecap](https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-linecap)", "description": "To set the style of the progress linecap, De<PERSON>ult: `round`"}, "a-progress/success": {"type": "{ percent: number, strokeColor: string }", "description": "Configs of successfully progress bar, Default: -"}, "a-progress/title": {"type": "string", "description": "html dom title, Default: -"}, "a-progress/trailColor": {"type": "string", "description": "The color of unfilled part, Default: -"}, "a-progress/type": {"type": "string", "description": "To set the type, options: `line` `circle` `dashboard`, Default: `line`"}, "a-progress/size": {"type": "number | [number, number] | \"small\" | \"default\"", "description": "Progress size, Default: \"default\""}, "a-qrcode/value": {"type": "string", "description": "scanned link, Default: -"}, "a-qrcode/type": {"type": "`'canvas'` | `'svg'`", "description": "render type, Default: `canvas`"}, "a-qrcode/icon": {"type": "string", "description": "include image url (only image link are supported), Default: -"}, "a-qrcode/size": {"type": "number", "description": "QRCode size, Default: 128"}, "a-qrcode/iconSize": {"type": "number", "description": "include image size, Default: 32"}, "a-qrcode/color": {"type": "string", "description": "QRCode Color, Default: `#000`"}, "a-qrcode/bgColor": {"type": "string", "description": "QRCode Background Color, Default: `transparent`"}, "a-qrcode/bordered": {"type": "boolean", "description": "Whether has border style, Default: `true`"}, "a-qrcode/errorLevel": {"type": "`'L'` | `'M'` | `'Q'` | `'H'`", "description": "Error Code Level, Default: `'M'`"}, "a-qrcode/status": {"type": "`active` | `expired` | `loading` | `scanned`", "description": "QRCode status, Default: `active`"}, "a-radio/blur()": {"type": "", "description": "remove focus, Default: undefined"}, "a-radio/focus()": {"type": "", "description": "get focus, De<PERSON>ult: undefined"}, "a-radio-radio-button/autofocus": {"type": "boolean", "description": "get focus when component mounted, Default: false"}, "a-radio-radio-button/checked(v-model)": {"type": "boolean", "description": "Specifies whether the radio is selected., Default: -"}, "a-radio-radio-button/disabled": {"type": "boolean", "description": "Disable radio, Default: false"}, "a-radio-radio-button/value": {"type": "any", "description": "According to value for comparison, to determine whether the selected, Default: -"}, "a-radio-group/buttonStyle": {"type": "`outline` | `solid`", "description": "style type of radio button, Default: `outline`"}, "a-radio-group/disabled": {"type": "boolean", "description": "Disable all radio buttons, Default: false"}, "a-radio-group/name": {"type": "string", "description": "The `name` property of all `input[type=\"radio\"]` children, Default: -"}, "a-radio-group/options": {"type": "string[] | number[] | Array&lt;{ label: string value: string disabled?: boolean }>", "description": "set children optional, Default: -"}, "a-radio-group/optionType": {"type": "`default` | `button`", "description": "Set Radio optionType, Default: `default`"}, "a-radio-group/size": {"type": "`large` | `default` | `small`", "description": "size for radio button style, Default: `default`"}, "a-radio-group/value(v-model)": {"type": "any", "description": "Used for setting the currently selected value., Default: -"}, "a-radio-group/change": {"type": "Function(e:Event)", "description": "The callback function that is triggered when the state changes., Default: undefined"}, "a-rate/allowClear": {"type": "boolean", "description": "whether to allow clear when click again, De<PERSON><PERSON>: true"}, "a-rate/allowHalf": {"type": "boolean", "description": "whether to allow semi selection, Default: false"}, "a-rate/autofocus": {"type": "boolean", "description": "get focus when component mounted, Default: false"}, "a-rate/character": {"type": "string | slot", "description": "custom character of rate, Default: `<StarOutlined />`"}, "a-rate/count": {"type": "number", "description": "star count, De<PERSON>ult: 5"}, "a-rate/disabled": {"type": "boolean", "description": "read only, unable to interact, Default: false"}, "a-rate/tooltips": {"type": "string[]", "description": "Customize tooltip by each character, Default: -"}, "a-rate/value(v-model)": {"type": "number", "description": "current value, Default: -"}, "a-result/extra": {"type": "slot", "description": "operating area, Default: -"}, "a-result/icon": {"type": "slot", "description": "custom back icon, Default: -"}, "a-result/status": {"type": "`success` | `error` | `info` | `warning` | `404` | `403` | `500`", "description": "result status,decide icons and colors, Default: 'info'"}, "a-result/subTitle": {"type": "string | VNode | slot", "description": "subTitle string, Default: -"}, "a-result/title": {"type": "string | VNode | slot", "description": "title string, Default: -"}, "a-segmented/block": {"type": "boolean", "description": "Option to fit width to its parent\\'s width, Default: false"}, "a-segmented/disabled": {"type": "boolean", "description": "Disable all segments, Default: false"}, "a-segmented/options": {"type": "string[] | number[] | SegmentedOption[]", "description": "Set children optional, Default: []"}, "a-segmented/size": {"type": "`large` | `middle` | `small`", "description": "The size of the Segmented., Default: -"}, "a-segmented/value": {"type": "string | number", "description": "Currently selected value, Default: "}, "a-segmented/label": {"type": "v-slot:label=\"SegmentedBaseOption\"", "description": "custom label by slot, Default: "}, "a-select/allowClear": {"type": "boolean", "description": "Show clear button., De<PERSON>ult: false"}, "a-select/autoClearSearchValue": {"type": "boolean", "description": "Whether the current search will be cleared on selecting an item. Only applies when `mode` is set to `multiple` or `tags`., Default: true"}, "a-select/autofocus": {"type": "boolean", "description": "Get focus by default, Default: false"}, "a-select/bordered": {"type": "boolean", "description": "Whether has border style, Default: true"}, "a-select/clearIcon": {"type": "VNode | slot", "description": "The custom clear icon, Default: -"}, "a-select/defaultActiveFirstOption": {"type": "boolean", "description": "Whether active first option by default, Default: true"}, "a-select/defaultOpen": {"type": "boolean", "description": "Initial open state of dropdown, De<PERSON>ult: -"}, "a-select/disabled": {"type": "boolean", "description": "Whether disabled select, Default: false"}, "a-select/popupClassName": {"type": "string", "description": "className of dropdown menu, Default: -"}, "a-select/dropdownMatchSelectWidth": {"type": "boolean | number", "description": "Determine whether the dropdown menu and the select input are the same width. De<PERSON><PERSON> set `min-width` same as input. Will ignore when value less than select width. `false` will disable virtual scroll, Default: true"}, "a-select/dropdownMenuStyle": {"type": "object", "description": "additional style applied to dropdown menu, Default: -"}, "a-select/dropdownRender": {"type": "({menuNode: VNode, props}) => VNode | v-slot", "description": "Customize dropdown content, Default: -"}, "a-select/dropdownStyle": {"type": "object", "description": "style of dropdown menu, Default: -"}, "a-select/fieldNames": {"type": "object", "description": "Customize node label, value, options field name, Default: { label: `label`, value: `value`, options: `options` }"}, "a-select/filterOption": {"type": "`boolean` | `function(inputValue, option)`", "description": "If true, filter options by input, if function, filter options against it. The function will receive two arguments, `inputValue` and `option`, if the function returns `true`, the option will be included in the filtered set; Otherwise, it will be excluded., Default: true"}, "a-select/filterSort": {"type": "(optionA: Option, optionB: Option) => number", "description": "Sort function for search options sorting, see [Array.sort](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort)'s compareFunction, Default: -"}, "a-select/firstActiveValue": {"type": "string | string[]", "description": "Value of action option by default, Default: -"}, "a-select/getPopupContainer": {"type": "function(triggerNode)", "description": "Parent Node which the selector should be rendered to. Default to `body`. When position issues happen, try to modify it into scrollable content and position it relative., Default: () => document.body"}, "a-select/labelInValue": {"type": "boolean", "description": "whether to embed label in value, turn the format of value from `string` to `{key: string, label: vNodes, originLabel: any}`, originLabel (3.1) maintains the original type. If the node is constructed through a-select-option children, the value is a function (the default slot of a-select-option), Default: false"}, "a-select/listHeight": {"type": "number", "description": "Config popup height, De<PERSON>ult: 256"}, "a-select/loading": {"type": "boolean", "description": "indicate loading state, Default: false"}, "a-select/maxTagCount": {"type": "number", "description": "Max tag count to show, De<PERSON>ult: -"}, "a-select/maxTagPlaceholder": {"type": "slot | function(omittedValues)", "description": "Placeholder for not showing tags, Default: -"}, "a-select/maxTagTextLength": {"type": "number", "description": "Max text length to show, Default: -"}, "a-select/menuItemSelectedIcon": {"type": "VNode | slot", "description": "The custom menuItemSelected icon, Default: -"}, "a-select/mode": {"type": "'multiple' | 'tags'", "description": "Set mode of Select, Default: -"}, "a-select/notFoundContent": {"type": "string|slot", "description": "Specify content to show when no result matches.., Default: `Not Found`"}, "a-select/open": {"type": "boolean", "description": "Controlled open state of dropdown, Default: -"}, "a-select/option": {"type": "v-slot:option=\"{value, label, [disabled, key, title]}\"", "description": "custom render option by slot, Default: -"}, "a-select/optionFilterProp": {"type": "string", "description": "Which prop value of option will be used for filter if filterOption is true, Default: value"}, "a-select/optionLabelProp": {"type": "string", "description": "Which prop value of option will render as content of select., Default: `children` | `label`(when use options)"}, "a-select/options": {"type": "Array&lt;{value, label, [disabled, key, title]}>", "description": "Data of the selectOption, manual construction work is no longer needed if this property has been set, Default: \\[]"}, "a-select/placeholder": {"type": "string|slot", "description": "Placeholder of select, Default: -"}, "a-select/placement": {"type": "`bottomLeft` `bottomRight` `topLeft` `topRight`", "description": "The position where the selection box pops up, Default: bottomLeft"}, "a-select/removeIcon": {"type": "VNode | slot", "description": "The custom remove icon, Default: -"}, "a-select/searchValue": {"type": "string", "description": "The current input \"search\" text, Default: -"}, "a-select/showArrow": {"type": "boolean", "description": "Whether to show the drop-down arrow, Default: single:true, multiple:false"}, "a-select/showSearch": {"type": "boolean", "description": "Whether select is searchable, Default: single:false, multiple:true"}, "a-select/size": {"type": "string", "description": "Size of Select input. `default` `large` `small`, Default: default"}, "a-select/status": {"type": "'error' | 'warning'", "description": "Set validation status, Default: -"}, "a-select/suffixIcon": {"type": "VNode | slot", "description": "The custom suffix icon, Default: -"}, "a-select/tagRender": {"type": "slot | (props) => any", "description": "Customize tag render, only applies when `mode` is set to `multiple` or `tags`, Default: -"}, "a-select/tokenSeparators": {"type": "string[]", "description": "Separator used to tokenize, only applies when `mode=\"tags\"`, Default: -"}, "a-select/value(v-model)": {"type": "string|number|string[]|number[]", "description": "Current selected option., De<PERSON>ult: -"}, "a-select/virtual": {"type": "boolean", "description": "Disable virtual scroll when set to false, Default: true"}, "a-select/blur()": {"type": "", "description": "Remove focus, Default: undefined"}, "a-select/focus()": {"type": "", "description": "Get focus, Default: undefined"}, "a-skeleton/active": {"type": "boolean", "description": "Show animation effect, Default: false"}, "a-skeleton/avatar": {"type": "boolean | [SkeletonAvatarProps](#skeletonavatarprops)", "description": "Show avatar placeholder, De<PERSON><PERSON>: false"}, "a-skeleton/loading": {"type": "boolean", "description": "Display the skeleton when `true`, Default: -"}, "a-skeleton/paragraph": {"type": "boolean | [SkeletonParagraphProps](#skeletonparagraphprops)", "description": "Show paragraph placeholder, Default: true"}, "a-skeleton/title": {"type": "boolean | [SkeletonTitleProps](#skeletontitleprops)", "description": "Show title placeholder, De<PERSON>ult: true"}, "a-skeleton-avatar-props/shape": {"type": "`circle` | `square`", "description": "Set the shape of avatar, De<PERSON><PERSON>: -"}, "a-skeleton-avatar-props/size": {"type": "number | `large` | `small` | `default`", "description": "Set the size of avatar, De<PERSON><PERSON>: -"}, "a-skeleton-title-props/width": {"type": "number | string", "description": "Set the width of title, De<PERSON><PERSON>: -"}, "a-skeleton-paragraph-props/rows": {"type": "number", "description": "Set the row count of paragraph, De<PERSON><PERSON>: -"}, "a-skeleton-paragraph-props/width": {"type": "number | string | Array&lt;number | string>", "description": "Set the width of paragraph. When width is an Array, it can set the width of each row. Otherwise only set the last row width, Default: -"}, "a-skeleton-button-props/active": {"type": "boolean", "description": "Show animation effect, Default: false"}, "a-skeleton-button-props/block": {"type": "boolean", "description": "Option to fit button width to its parent width, Default: false"}, "a-skeleton-button-props/shape": {"type": "`circle` | `round` | `default`", "description": "Set the shape of button, De<PERSON>ult: -"}, "a-skeleton-button-props/size": {"type": "`large` | `small` | `default`", "description": "Set the size of button, De<PERSON>ult: -"}, "a-skeleton-input-props/active": {"type": "boolean", "description": "Show animation effect, Default: false"}, "a-skeleton-input-props/size": {"type": "`large` | `small` | `default`", "description": "Set the size of input, De<PERSON>ult: -"}, "a-slider/autofocus": {"type": "boolean", "description": "get focus when component mounted, Default: false"}, "a-slider/disabled": {"type": "boolean", "description": "If true, the slider will not be intractable., Default: false"}, "a-slider/dots": {"type": "boolean", "description": "Whether the thumb can drag over tick only., Default: false"}, "a-slider/handleStyle": {"type": "CSSProperties", "description": "The style of slider handle, Default: -"}, "a-slider/included": {"type": "boolean", "description": "Make effect when `marks` not null, `true` means containment and `false` means coordinative, Default: true"}, "a-slider/mark": {"type": "v-slot:mark", "description": "Custom tick mark of <PERSON><PERSON><PERSON>,, Default: { point: number, label: any }"}, "a-slider/marks": {"type": "object", "description": "Tick mark of Slider, type of key must be `number`, and must in closed interval \\[min, max], each mark can declare its own style., Default: { number: string|VNode } or { number: { style: object, label: string|VNode } } or { number: () => VNode }"}, "a-slider/max": {"type": "number", "description": "The maximum value the slider can slide to, Default: 100"}, "a-slider/min": {"type": "number", "description": "The minimum value the slider can slide to., Default: 0"}, "a-slider/range": {"type": "boolean", "description": "dual thumb mode, Default: false"}, "a-slider/reverse": {"type": "boolean", "description": "reverse the component, Default: false"}, "a-slider/step": {"type": "number|null", "description": "The granularity the slider can step through values. Must greater than 0, and be divided by (max - min) . When `marks` no null, `step` can be `null`., Default: 1"}, "a-slider/trackStyle": {"type": "CSSProperties", "description": "The style of slider track, Default: -"}, "a-slider/value(v-model)": {"type": "number|number[]", "description": "The value of slider. When `range` is `false`, use `number`, otherwise, use `[number, number]`, Default: "}, "a-slider/vertical": {"type": "Boolean", "description": "If true, the slider will be vertical., Default: false"}, "a-slider/tipFormatter": {"type": "Function|null", "description": "Slider will pass its value to `tipF<PERSON>atter`, and display its value in Tooltip, and hide Tooltip when return value is null., Default: IDENTITY"}, "a-slider/tooltipPlacement": {"type": "string", "description": "Set Tooltip display position. Ref [`Tooltip`](/components/tooltip/)., Default: "}, "a-slider/tooltipOpen": {"type": "Boolean", "description": "If true, <PERSON><PERSON><PERSON> will show always, or it will not show anyway, even if dragging or hovering., De<PERSON>ult: "}, "a-slider/getTooltipPopupContainer": {"type": "Function", "description": "The DOM container of the Tooltip, the default behavior is to create a div element in body., Default: () => document.body"}, "a-space/align": {"type": "`start` | `end` |`center` |`baseline`", "description": "Align items, Default: -"}, "a-space/direction": {"type": "`vertical` | `horizontal`", "description": "The space direction, Default: `horizontal`"}, "a-space/size": {"type": "`small` | `middle` | `large` | `number`", "description": "The space size, Default: `small`"}, "a-space/split": {"type": "VueNode | v-slot", "description": "Set split, Default: -"}, "a-space/wrap": {"type": "boolean", "description": "Auto wrap line, when `horizontal` effective, Default: false"}, "a-space-compact/block": {"type": "boolean", "description": "Option to fit width to its parent\\'s width, Default: false"}, "a-space-compact/direction": {"type": "`vertical` | `horizontal`", "description": "Set direction of layout, Default: `horizontal`"}, "a-space-compact/size": {"type": "`large` | `middle` | `small`", "description": "Set child component size, Default: `middle`"}, "a-spin/delay": {"type": "number (milliseconds)", "description": "specifies a delay in milliseconds for loading state (prevent flush), Default: -"}, "a-spin/indicator": {"type": "vNode |slot", "description": "vue node of the spinning indicator, Default: -"}, "a-spin/size": {"type": "string", "description": "size of Spin, options: `small`, `default` and `large`, Default: `default`"}, "a-spin/spinning": {"type": "boolean", "description": "whether Spin is visible, Default: true"}, "a-spin/tip": {"type": "string | slot", "description": "customize description content when <PERSON> has children, Default: -"}, "a-spin/wrapperClassName": {"type": "string", "description": "className of wrapper when <PERSON> has children, De<PERSON>ult: -"}, "a-statistic/decimalSeparator": {"type": "string", "description": "decimal separator, Default: ."}, "a-statistic/formatter": {"type": "v-slot |({value}) => VNode", "description": "customize value display logic, Default: -"}, "a-statistic/groupSeparator": {"type": "string", "description": "group separator, Default: ,"}, "a-statistic/precision": {"type": "number", "description": "precision of input value, Default: -"}, "a-statistic/prefix": {"type": "string | v-slot", "description": "prefix node of value, Default: -"}, "a-statistic/suffix": {"type": "string | v-slot", "description": "suffix node of value, Default: -"}, "a-statistic/title": {"type": "string | v-slot", "description": "Display title, Default: -"}, "a-statistic/value": {"type": "string | number", "description": "Display value, Default: -"}, "a-statistic/valueStyle": {"type": "style", "description": "Set value css style, Default: -"}, "a-statistic-countdown/format": {"type": "string", "description": "Format as [dayjs](https://day.js.org/), Default: 'HH:mm:ss'"}, "a-statistic-countdown/prefix": {"type": "string | v-slot", "description": "prefix node of value, Default: -"}, "a-statistic-countdown/suffix": {"type": "string | v-slot", "description": "suffix node of value, Default: -"}, "a-statistic-countdown/title": {"type": "string | v-slot", "description": "Display title, Default: -"}, "a-statistic-countdown/value": {"type": "number | dayjs", "description": "Set target countdown time, De<PERSON>ult: -"}, "a-statistic-countdown/valueStyle": {"type": "style", "description": "Set value css style, Default: -"}, "a-statistic-countdown/finish": {"type": "() => void", "description": "Trigger when time's up, De<PERSON><PERSON>: -"}, "a-steps/current(v-model)": {"type": "number", "description": "to set the current step, counting from 0. You can overwrite this state by using `status` of `Step`, support v-model after 1.5.0, Default: 0"}, "a-steps/direction": {"type": "string", "description": "to specify the direction of the step bar, `horizontal` and `vertical` are currently supported, Default: `horizontal`"}, "a-steps/initial": {"type": "number", "description": "set the initial step, counting from 0, <PERSON><PERSON><PERSON>: 0"}, "a-steps/labelPlacement": {"type": "string", "description": "support vertical title and description, Default: `horizontal`"}, "a-steps/percent": {"type": "number", "description": "Progress circle percentage of current step in `process` status (only works on basic Steps), Default: -"}, "a-steps/progressDot": {"type": "Boolean or v-slot:progressDot=\"{index, status, title, description, prefixCls, iconDot}\"", "description": "Steps with progress dot style, customize the progress dot by setting a scoped slot. labelPlacement will be `vertical`, Default: false"}, "a-steps/responsive": {"type": "boolean", "description": "change to vertical direction when screen width smaller than `532px`, Default: true"}, "a-steps/size": {"type": "string", "description": "to specify the size of the step bar, `default` and `small` are currently supported, Default: `default`"}, "a-steps/status": {"type": "string", "description": "to specify the status of current step, can be set to one of the following values: `wait` `process` `finish` `error`, Default: `process`"}, "a-steps/type": {"type": "string", "description": "Type of steps, can be set to one of the following values: `default`, `navigation`, Default: `default`"}, "a-steps/items": {"type": "[StepItem](#stepsstep)", "description": "StepItem content, Default: []"}, "a-steps/change": {"type": "(current) => void", "description": "Trigger when Step is changed, Default: -"}, "a-steps-step/description": {"type": "string|slot", "description": "description of the step, optional property, Default: -"}, "a-steps-step/disabled": {"type": "boolean", "description": "Disable click, Default: false"}, "a-steps-step/icon": {"type": "string|slot", "description": "icon of the step, optional property, Default: -"}, "a-steps-step/status": {"type": "string", "description": "to specify the status. It will be automatically set by `current` of `Steps` if not configured. Optional values are: `wait` `process` `finish` `error`, Default: `wait`"}, "a-steps-step/subTitle": {"type": "string|slot", "description": "Subtitle of the step, De<PERSON>ult: -"}, "a-steps-step/title": {"type": "string|slot", "description": "title of the step, Default: -"}, "a-switch/autofocus": {"type": "boolean", "description": "get focus when component mounted, Default: false"}, "a-switch/checked(v-model)": {"type": "checkedValue | unCheckedValue", "description": "determine whether the `Switch` is checked, Default: false"}, "a-switch/checkedChildren": {"type": "string|slot", "description": "content to be shown when the state is checked, Default: "}, "a-switch/checkedValue": {"type": "boolean | string | number", "description": "value for checked state, Default: true"}, "a-switch/disabled": {"type": "boolean", "description": "Disable switch, Default: false"}, "a-switch/loading": {"type": "boolean", "description": "loading state of switch, Default: false"}, "a-switch/size": {"type": "string", "description": "the size of the `Switch`, options: `default` `small`, Default: default"}, "a-switch/unCheckedChildren": {"type": "string|slot", "description": "content to be shown when the state is unchecked, Default: "}, "a-switch/unCheckedValue": {"type": "boolean | string | number", "description": "value for unchecked state, Default: false"}, "a-table/bodyCell": {"type": "v-slot:bodyCell=\"{text, record, index, column}\"", "description": "custom body cell by slot, Default: -"}, "a-table/bordered": {"type": "boolean", "description": "Whether to show all table borders, Default: `false`"}, "a-table/childrenColumnName": {"type": "string", "description": "The column contains children to display, Default: `children`"}, "a-table/columns": {"type": "array", "description": "Columns of table [config](#column), Default: -"}, "a-table/components": {"type": "object", "description": "Override default table elements, Default: -"}, "a-table/customFilterDropdown": {"type": "v-slot:customFilterDropdown=\"[FilterDropdownProps](#filterdropdownprops)\"", "description": "Customized filter overlay, need set `column.customFilterDropdown`, Default: -"}, "a-table/customFilterIcon": {"type": "v-slot:customFilterIcon=\"{filtered, column}\"", "description": "Customized filter icon, Default: -"}, "a-table/customHeaderRow": {"type": "Function(columns, index)", "description": "Set props on per header row, Default: -"}, "a-table/customRow": {"type": "Function(record, index)", "description": "Set props on per row, De<PERSON>ult: -"}, "a-table/dataSource": {"type": "any[]", "description": "Data record array to be displayed, Default: -"}, "a-table/defaultExpandAllRows": {"type": "boolean", "description": "Expand all rows initially, Default: `false`"}, "a-table/defaultExpandedRowKeys": {"type": "string[]", "description": "Initial expanded row keys, Default: -"}, "a-table/emptyText": {"type": "v-slot:emptyText", "description": "Customize the display content when empty data, Default: -"}, "a-table/expandedRowKeys(v-model)": {"type": "string[]", "description": "Current expanded row keys, Default: -"}, "a-table/expandedRowRender": {"type": "Function({record, index, indent, expanded}):VNode|v-slot", "description": "Expanded container render for each row, Default: -"}, "a-table/expandFixed": {"type": "boolean | string", "description": "Set column to be fixed: `true`(same as left) `'left'` `'right'`, Default: false"}, "a-table/expandColumnTitle": {"type": "v-slot", "description": "Set the title of the expand column, De<PERSON>ult: -"}, "a-table/expandIcon": {"type": "Function(props):VNode | v-slot:expandIcon=\"props\"", "description": "Customize row expand Icon., Default: -"}, "a-table/expandRowByClick": {"type": "boolean", "description": "Whether to expand row by clicking anywhere in the whole row, Default: `false`"}, "a-table/footer": {"type": "Function(currentPageData)| v-slot:footer=\"currentPageData\"", "description": "Table footer renderer, De<PERSON><PERSON>: "}, "a-table/getPopupContainer": {"type": "(triggerNode) => HTMLElement", "description": "the render container of dropdowns in table, Default: `() => TableHtmlElement`"}, "a-table/headerCell": {"type": "v-slot:headerCell=\"{title, column}\"", "description": "custom head cell by slot, Default: -"}, "a-table/indentSize": {"type": "number", "description": "Indent size in pixels of tree data, Default: 15"}, "a-table/loading": {"type": "boolean|[object](/components/spin)", "description": "Loading status of table, Default: `false`"}, "a-table/locale": {"type": "object", "description": "i18n text including filter, sort, empty text, etc, Default: filterConfirm: 'Ok' <br /> filterReset: 'Reset' <br /> emptyText: 'No Data'"}, "a-table/pagination": {"type": "object | `false`", "description": "Config of pagination. You can ref table pagination [config](#pagination) or full [`pagination`](/components/pagination/) document, hide it by setting it to `false`, Default: "}, "a-table/rowClassName": {"type": "Function(record, index):string", "description": "Row's classN<PERSON>, De<PERSON>ult: -"}, "a-table/rowExpandable": {"type": "(record) => boolean", "description": "Enable row can be expandable, Default: -"}, "a-table/rowKey": {"type": "string|Function(record, index):string", "description": "<PERSON>'s unique key, could be a string or function that returns a string, Default: `key`"}, "a-table/rowSelection": {"type": "object", "description": "Row selection [config](#rowselection), Default: null"}, "a-table/scroll": {"type": "object", "description": "Whether the table can be scrollable, [config](#scroll), Default: -"}, "a-table/showExpandColumn": {"type": "boolean", "description": "Show expand column, Default: true"}, "a-table/showHeader": {"type": "boolean", "description": "Whether to show table header, Default: `true`"}, "a-table/showSorterTooltip": {"type": "boolean | [Tooltip props](/components/tooltip/#api)", "description": "The header show next sorter direction tooltip. It will be set as the property of Tooltip if its type is object, Default: true"}, "a-table/size": {"type": "`middle` | `small` | `large`", "description": "Size of table, Default: `large`"}, "a-table/sortDirections": {"type": "Array", "description": "Supported sort way, could be `ascend`, `descend`, Default: \\[`ascend`, `descend`]"}, "a-table/sticky": {"type": "boolean | `{offsetHeader?: number, offsetScroll?: number, getContainer?: () => HTMLElement}`", "description": "Set sticky header and scroll bar, Default: -"}, "a-table/summary": {"type": "v-slot:summary", "description": "Summary content, Default: -"}, "a-table/tableLayout": {"type": "- | 'auto' | 'fixed'", "description": "[table-layout](https://developer.mozilla.org/en-US/docs/Web/CSS/table-layout) attribute of table element, Default: -<hr />`fixed` when header/columns are fixed, or using `column.ellipsis`"}, "a-table/title": {"type": "Function(currentPageData)| v-slot:title=\"currentPageData\"", "description": "Table title renderer, De<PERSON>ult: "}, "a-table/transformCellText": {"type": "Function({ text, column, record, index }) => any, The `text` here is the data processed by other defined cell api, and it may be of type VNode | string | number", "description": "The data can be changed again before rendering, generally used for the default configuration of empty data. You can configured globally through [ConfigProvider](/components/config-provider-cn/), Default: -"}, "a-tabs/activeKey(v-model)": {"type": "string", "description": "Current TabPane's key, Default: -"}, "a-tabs/animated": {"type": "boolean | {inkBar:boolean, tabPane:boolean}", "description": "Whether to change tabs with animation. Only works while tabPosition=`\"top\"` | `\"bottom\"`, Default: `true`, `false` when `type=\"card\"`"}, "a-tabs/destroyInactiveTabPane": {"type": "boolean", "description": "Whether destroy inactive TabPane when change tab, Default: false"}, "a-tabs/hideAdd": {"type": "boolean", "description": "Hide plus icon or not. Only works while `type=\"editable-card\"`, Default: `false`"}, "a-tabs/size": {"type": "`large` | `middle` | `small`", "description": "preset tab bar size, Default: `middle`"}, "a-tabs/tabBarGutter": {"type": "number", "description": "The gap between tabs, Default: -"}, "a-tabs/tabBarStyle": {"type": "CSSProperties", "description": "Tab bar style object, Default: -"}, "a-tabs/tabPosition": {"type": "`top` | `right` | `bottom` | `left`", "description": "Position of tabs, Default: `top`"}, "a-tabs/type": {"type": "`line` | `card` | `editable-card`", "description": "Basic style of tabs, Default: `line`"}, "a-tabs/addIcon": {"type": "-", "description": "Customize add icon, Default: -"}, "a-tabs/leftExtra": {"type": "-", "description": "Extra content in tab bar left, Default: -"}, "a-tabs/moreIcon": {"type": "-", "description": "The custom icon of ellipsis, Default: -"}, "a-tabs/renderTabBar": {"type": "{ DefaultTabBar }", "description": "Replace the TabBar, <PERSON><PERSON><PERSON>: "}, "a-tabs/rightExtra": {"type": "-", "description": "Extra content in tab bar right, Default: -"}, "a-tabs/change": {"type": "Function(activeKey) {}", "description": "Callback executed when active tab is changed, Default: undefined"}, "a-tabs/edit": {"type": "(action === 'add' ? event : target<PERSON>ey, action): void", "description": "Callback executed when tab is added or removed. Only works while `type=\"editable-card\"`, Default: undefined"}, "a-tabs/nextClick": {"type": "Function", "description": "Callback executed when next button is clicked, Default: undefined"}, "a-tabs/prevClick": {"type": "Function", "description": "Callback executed when prev button is clicked, Default: undefined"}, "a-tabs/tabClick": {"type": "Function", "description": "Callback executed when tab is clicked, Default: undefined"}, "a-tabs-tab-pane/forceRender": {"type": "boolean", "description": "Forced render of content in tabs, not lazy render after clicking on tabs, Default: false"}, "a-tabs-tab-pane/key": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>'s key, De<PERSON>ult: -"}, "a-tabs-tab-pane/tab": {"type": "-", "description": "Show text in <PERSON><PERSON><PERSON><PERSON>'s head, De<PERSON><PERSON>: undefined"}, "a-tabs-tab-pane/closeIcon": {"type": "-", "description": "Customize close icon, Only works while `type=\"editable-card\"`, Default: undefined"}, "a-tag/closable": {"type": "boolean", "description": "Whether the Tag can be closed, Default: `false`"}, "a-tag/closeIcon": {"type": "VNode | slot", "description": "Custom close icon, Default: -"}, "a-tag/color": {"type": "string", "description": "Color of the Tag, Default: -"}, "a-tag/icon": {"type": "VNode | slot", "description": "Set the icon of tag, De<PERSON>ult: -"}, "a-tag/bordered": {"type": "boolean", "description": "Whether has border style, Default: `true`"}, "a-tag/close": {"type": "(e) => void", "description": "Callback executed when tag is closed, Default: undefined"}, "a-tag-checkable-tag/checked(v-model)": {"type": "boolean", "description": "Checked status of Tag, Default: `false`"}, "a-tag-checkable-tag/change": {"type": "(checked) => void", "description": "Callback executed when Tag is checked/unchecked, Default: undefined"}, "a-time-picker/allowClear": {"type": "boolean", "description": "Whether allow clearing text, Default: true"}, "a-time-picker/autofocus": {"type": "boolean", "description": "If get focus when component mounted, Default: false"}, "a-time-picker/bordered": {"type": "boolean", "description": "Whether has border style, Default: true"}, "a-time-picker/clearIcon": {"type": "v-slot:clearIcon", "description": "The custom clear icon, Default: -"}, "a-time-picker/clearText": {"type": "string", "description": "The clear tooltip of icon, De<PERSON>ult: clear"}, "a-time-picker/disabled": {"type": "boolean", "description": "Determine whether the TimePicker is disabled, Default: false"}, "a-time-picker/disabledTime": {"type": "[DisabledTime](#disabledtime)", "description": "To specify the time that cannot be selected, Default: -"}, "a-time-picker/format": {"type": "string", "description": "To set the time format, Default: `HH:mm:ss`"}, "a-time-picker/getPopupContainer": {"type": "function(trigger)", "description": "To set the container of the floating layer, while the default is to create a div element in body, Default: -"}, "a-time-picker/hideDisabledOptions": {"type": "boolean", "description": "Whether hide the options that can not be selected, Default: false"}, "a-time-picker/hourStep": {"type": "number", "description": "Interval between hours in picker, De<PERSON>ult: 1"}, "a-time-picker/inputReadOnly": {"type": "boolean", "description": "Set the `readonly` attribute of the input tag (avoids virtual keyboard on touch devices), Default: false"}, "a-time-picker/minuteStep": {"type": "number", "description": "Interval between minutes in picker, <PERSON><PERSON><PERSON>: 1"}, "a-time-picker/open(v-model)": {"type": "boolean", "description": "Whether to popup panel, Default: false"}, "a-time-picker/placeholder": {"type": "string | [string, string]", "description": "Display when there's no value, De<PERSON>ult: `Select a time`"}, "a-time-picker/placement": {"type": "`bottomLeft` `bottomRight` `topLeft` `topRight`", "description": "The position where the selection box pops up, Default: bottomLeft"}, "a-time-picker/popupClassName": {"type": "string", "description": "The className of panel, Default: -"}, "a-time-picker/popupStyle": {"type": "CSSProperties", "description": "The style of panel, Default: -"}, "a-time-picker/renderExtraFooter": {"type": "v-slot:renderExtraFooter", "description": "Called from time picker panel to render some addon to its bottom, Default: -"}, "a-time-picker/secondStep": {"type": "number", "description": "Interval between seconds in picker, <PERSON><PERSON><PERSON>: 1"}, "a-time-picker/showNow": {"type": "boolean", "description": "Whether to show `Now` button on panel, De<PERSON><PERSON>: -"}, "a-time-picker/suffixIcon": {"type": "v-slot:suffixIcon", "description": "The custom suffix icon, Default: -"}, "a-time-picker/use12Hours": {"type": "boolean", "description": "Display as 12 hours format, with default format `h:mm:ss a`, Default: false"}, "a-time-picker/value(v-model)": {"type": "[dayjs](https://day.js.org/)", "description": "To set time, Default: -"}, "a-time-picker/valueFormat": {"type": "string, [date formats](https://day.js.org/docs/en/display/format)", "description": "optional, format of binding value. If not specified, the binding value will be a Date object, Default: -"}, "a-timeline/mode": {"type": "`left` | `alternate` | `right`", "description": "By sending `alternate` the timeline will distribute the nodes to the left and right., De<PERSON>ult: `left`"}, "a-timeline/pending": {"type": "boolean|string|slot", "description": "Set the last ghost node's existence or its content, Default: `false`"}, "a-timeline/pendingDot": {"type": "string|slot", "description": "Set the dot of the last ghost node when pending is true, Default: `<LoadingOutlined />`"}, "a-timeline/reverse": {"type": "boolean", "description": "reverse nodes or not, Default: false"}, "a-timeline-item/color": {"type": "string", "description": "Set the circle's color to `blue`, `red`, `green` or other custom colors, Default: `blue`"}, "a-timeline-item/dot": {"type": "string|slot", "description": "Customize timeline dot, Default: -"}, "a-timeline-item/label": {"type": "string | slot", "description": "Set the label, Default: -"}, "a-timeline-item/position": {"type": "`left` | `right`", "description": "Customize node position, Default: -"}, "a-tooltip/title": {"type": "string|slot", "description": "The text shown in the tooltip, Default: -"}, "a-tooltip/align": {"type": "Object", "description": "this value will be merged into placement's config, please refer to the settings [dom-align](https://github.com/yiminghe/dom-align), Default: -"}, "a-tooltip/arrowPointAtCenter": {"type": "boolean", "description": "Whether the arrow is pointed at the center of target, Default: `false`"}, "a-tooltip/autoAdjustOverflow": {"type": "boolean", "description": "Whether to adjust popup placement automatically when popup is off screen, Default: `true`"}, "a-tooltip/color": {"type": "string", "description": "The background color, Default: -"}, "a-tooltip/destroyTooltipOnHide": {"type": "boolean", "description": "Whether to destroy tooltip on hide, Default: false"}, "a-tooltip/getPopupContainer": {"type": "(triggerNode: HTMLElement) => HTMLElement", "description": "The DOM container of the tip, the default behavior is to create a `div` element in `body`., Default: () => document.body"}, "a-tooltip/mouseEnterDelay": {"type": "number", "description": "Delay in seconds, before tooltip is shown on mouse enter, Default: 0.1"}, "a-tooltip/mouseLeaveDelay": {"type": "number", "description": "Delay in seconds, before tooltip is hidden on mouse leave, Default: 0.1"}, "a-tooltip/overlayClassName": {"type": "string", "description": "Class name of the tooltip card, Default: -"}, "a-tooltip/overlayStyle": {"type": "object", "description": "Style of the tooltip card, Default: -"}, "a-tooltip/overlayInnerStyle": {"type": "object", "description": "Style of the tooltip inner content, Default: -"}, "a-tooltip/placement": {"type": "string", "description": "The position of the tooltip relative to the target, which can be one of `top` `left` `right` `bottom` `topLeft` `topRight` `bottomLeft` `bottomRight` `leftTop` `leftBottom` `rightTop` `rightBottom`, Default: `top`"}, "a-tooltip/trigger": {"type": "`hover` | `focus` | `click` | `contextmenu`", "description": "Tooltip trigger mode, Default: `hover`"}, "a-tooltip/open(v-model)": {"type": "boolean", "description": "Whether the floating tooltip card is open or not, Use `visible` under 4.0.0, Default: `false`"}, "a-tour/arrow": {"type": "`boolean`|`{ pointAtCenter: boolean}`", "description": "Whether to show the arrow, including the configuration whether to point to the center of the element, De<PERSON>ult: `true`"}, "a-tour/placement": {"type": "`left` `leftTop` `leftBottom` `right` `rightTop` `rightBottom` `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight`", "description": "Position of the guide card relative to the target element, Default: `bottom`"}, "a-tour/mask": {"type": "`boolean` | `{ style?: CSSProperties; color?: string; }`", "description": "Whether to enable masking, change mask style and fill color by pass custom props, Default: `true`"}, "a-tour/type": {"type": "`default` `primary`", "description": "Type, affects the background color and text color, Default: `default`"}, "a-tour/open": {"type": "`boolean`", "description": "Open tour, Default: -"}, "a-tour/current(v-model)": {"type": "`number`", "description": "What is the current step, De<PERSON>ult: -"}, "a-tour/scrollIntoViewOptions": {"type": "`boolean` | `ScrollIntoViewOptions`", "description": "support pass custom scrollIntoView options, Default: `true`"}, "a-tour/indicatorsRender": {"type": "`v-slot:indicatorsRender=\"{current, total}\"`", "description": "custom indicator, Default: -"}, "a-tour/zIndex": {"type": "`number`", "description": "Tour's zIndex, De<PERSON><PERSON>: `1001`"}, "a-tour-step/target": {"type": "`() => HTMLElement` `HTMLElement`", "description": "Get the element the guide card points to. Empty makes it show in center of screen, Default: -"}, "a-tour-step/arrow": {"type": "`boolean` `{ pointAtCenter: boolean}`", "description": "Whether to show the arrow, including the configuration whether to point to the center of the element, De<PERSON>ult: `true`"}, "a-tour-step/cover": {"type": "`VueNode`", "description": "Displayed pictures or videos, Default: -"}, "a-tour-step/title": {"type": "`VueNode`", "description": "title, Default: -"}, "a-tour-step/description": {"type": "`VueNode`", "description": "description, Default: -"}, "a-tour-step/placement": {"type": "`left` `leftTop` `leftBottom` `right` `rightTop` `rightBottom` `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight`", "description": "Position of the guide card relative to the target element, Default: `bottom`"}, "a-tour-step/mask": {"type": "`boolean` | `{ style?: CSSProperties; color?: string; }`", "description": "Whether to enable masking, change mask style and fill color by pass custom props, the default follows the `mask` property of Tour, Default: `true`"}, "a-tour-step/type": {"type": "`default` `primary`", "description": "Type, affects the background color and text color, Default: `default`"}, "a-tour-step/nextButtonProps": {"type": "`{ children: <PERSON><PERSON><PERSON><PERSON>; onClick: Function }`", "description": "Properties of the Next button, De<PERSON>ult: -"}, "a-tour-step/prevButtonProps": {"type": "`{ children: <PERSON><PERSON><PERSON><PERSON>; onClick: Function }`", "description": "Properties of the previous button, De<PERSON>ult: -"}, "a-tour-step/scrollIntoViewOptions": {"type": "`boolean` | `ScrollIntoViewOptions`", "description": "support pass custom scrollIntoView options, the default follows the `scrollIntoViewOptions` property of Tour, Default: `true`"}, "a-transfer/dataSource": {"type": "[{key: string.isRequired,title: string.isRequired,description: string,disabled: bool}]", "description": "Used for setting the source data. The elements that are part of this array will be present the left column. Except the elements whose keys are included in `targetKeys` prop., Default: \\[]"}, "a-transfer/disabled": {"type": "boolean", "description": "Whether disabled transfer, Default: false"}, "a-transfer/filterOption": {"type": "(inputValue, option): boolean", "description": "A function to determine whether an item should show in search result list, Default: "}, "a-transfer/footer": {"type": "slot=\"footer\" slot-scope=\"props\"", "description": "customize the progress dot by setting a scoped slot, Default: "}, "a-transfer/listStyle": {"type": "CSSProperties", "description": "A custom CSS style used for rendering the transfer columns., Default: "}, "a-transfer/locale": {"type": "object", "description": "i18n text including filter, empty text, item unit, etc, Default: `{ itemUnit: 'item', itemsUnit: 'items', notFoundContent: 'The list is empty', searchPlaceholder: 'Search here' }`"}, "a-transfer/oneWay": {"type": "boolean", "description": "Display as single direction style, Default: false"}, "a-transfer/operations": {"type": "string[]", "description": "A set of operations that are sorted from top to bottom., Default: \\['>', '&lt;']"}, "a-transfer/operationStyle": {"type": "CSSProperties", "description": "A custom CSS style used for rendering the operations column, Default: -"}, "a-transfer/pagination": {"type": "boolean | { pageSize: number, simple: boolean, showSizeChanger?: boolean, showLessItems?: boolean }", "description": "Use pagination. Not work in render props, Default: false"}, "a-transfer/render": {"type": "Function(record) | slot", "description": "The function to generate the item shown on a column. Based on an record (element of the dataSource array), this function should return a element which is generated from that record. Also, it can return a plain object with `value` and `label`, `label` is a element and `value` is for title, Default: "}, "a-transfer/selectAllLabels": {"type": "VueNode | ((info: { selectedCount: number; totalCount: number }) => VueNode);", "description": "A set of customized labels for select all checkboxes on the header, Default: -"}, "a-transfer/selectedKeys(v-model)": {"type": "string[]", "description": "A set of keys of selected items., Default: \\[]"}, "a-transfer/showSearch": {"type": "boolean", "description": "If included, a search box is shown on each column., Default: false"}, "a-transfer/showSelectAll": {"type": "boolean", "description": "Show select all checkbox on the header, Default: true"}, "a-transfer/status": {"type": "'error' | 'warning'", "description": "Set validation status, Default: -"}, "a-transfer/targetKeys(v-model)": {"type": "string[]", "description": "A set of keys of elements that are listed on the right column., Default: \\[]"}, "a-transfer/titles": {"type": "string[]", "description": "A set of titles that are sorted from left to right., Default: -"}, "a-tree/allowDrop": {"type": "({ dropNode, dropPosition }) => boolean", "description": "Whether to allow dropping on the node, Default: -"}, "a-tree/autoExpandParent": {"type": "boolean", "description": "Whether to automatically expand a parent treeNode, Default: false"}, "a-tree/blockNode": {"type": "boolean", "description": "Whether treeNode fill remaining horizontal space, Default: false"}, "a-tree/checkable": {"type": "boolean", "description": "Adds a `Checkbox` before the treeNodes, Default: false"}, "a-tree/checkedKeys(v-model)": {"type": "string[] | number[] | {checked: string\\[] | number\\[], halfChecked: string\\[] | number\\[]}", "description": "(Controlled) Specifies the keys of the checked treeNodes (PS: When this specifies the key of a treeNode which is also a parent treeNode, all the children treeNodes of will be checked; and vice versa, when it specifies the key of a treeNode which is a child treeNode, its parent treeNode will also be checked. When `checkable` and `checkStrictly` is true, its object has `checked` and `halfChecked` property. Regardless of whether the child or parent treeNode is checked, they won't impact each other., Default: \\[]"}, "a-tree/checkStrictly": {"type": "boolean", "description": "Check treeNode precisely; parent treeN<PERSON> and children treeNodes are not associated, Default: false"}, "a-tree/defaultExpandAll": {"type": "boolean", "description": "Whether to expand all treeNodes by default, Default: false"}, "a-tree/disabled": {"type": "bool", "description": "whether disabled the tree, Default: false"}, "a-tree/draggable": {"type": "boolean", "description": "Specifies whether this Tree is draggable (IE > 8), Default: false"}, "a-tree/expandedKeys(v-model)": {"type": "string[] | number[]", "description": "(Controlled) Specifies the keys of the expanded treeNodes, Default: \\[]"}, "a-tree/fieldNames": {"type": "object", "description": "Replace the title,key and children fields in treeNode with the corresponding fields in treeData, Default: { children:'children', title:'title', key:'key' }"}, "a-tree/filterTreeNode": {"type": "function(node)", "description": "Defines a function to filter (highlight) treeNodes. When the function returns `true`, the corresponding treeNode will be highlighted, Default: -"}, "a-tree/height": {"type": "number", "description": "Config virtual scroll height. Will not support horizontal scroll when enable this, Default: -"}, "a-tree/loadData": {"type": "function(node)", "description": "Load data asynchronously, Default: -"}, "a-tree/loadedKeys": {"type": "string[] | number[]", "description": "(Controlled) Set loaded tree nodes. Need work with `loadData`, Default: \\[]"}, "a-tree/multiple": {"type": "boolean", "description": "Allows selecting multiple treeNodes, Default: false"}, "a-tree/selectable": {"type": "boolean", "description": "whether can be selected, Default: true"}, "a-tree/selectedKeys(v-model)": {"type": "string[] | number[]", "description": "(Controlled) Specifies the keys of the selected treeNodes, Default: -"}, "a-tree/showIcon": {"type": "boolean", "description": "Shows the icon before a TreeNode's title. There is no default style; you must set a custom style for it if set to `true`, Default: false"}, "a-tree/showLine": {"type": "boolean | {showLeafIcon: boolean}(3.0+)", "description": "Shows a connecting line, Default: false"}, "a-tree/switcherIcon": {"type": "v-slot:switcherIcon=\"{active, checked, expanded, loading, selected, halfChecked, title, key, children, dataRef, data, defaultIcon, switcherCls}\"", "description": "customize collapse/expand icon of tree node, Default: -"}, "a-tree/title": {"type": "slot", "description": "custom title, Default: "}, "a-tree/treeData": {"type": "[TreeNode[]](#treenode)", "description": "treeNode of tree, please use `treeNodes` before v1.1.4, Default: -"}, "a-tree/virtual": {"type": "boolean", "description": "Disable virtual scroll when set to false, Default: true"}, "a-tree/scrollTo({ key: string | number; align?: 'top' | 'bottom' | 'auto'; offset?: number })": {"type": "", "description": "Scroll to key item in virtual scroll, Default: undefined"}, "a-tree-node/checkable": {"type": "boolean", "description": "When Tree is checkable, set TreeNode display Checkbox or not, Default: -"}, "a-tree-node/class": {"type": "string", "description": "className, Default: -"}, "a-tree-node/disableCheckbox": {"type": "boolean", "description": "Disables the checkbox of the treeNode, Default: false"}, "a-tree-node/disabled": {"type": "boolean", "description": "Disables the tree<PERSON><PERSON>, Default: false"}, "a-tree-node/icon": {"type": "slot|slot-scope", "description": "customize icon. When you pass component, whose render will receive full TreeNode props as component props, Default: -"}, "a-tree-node/isLeaf": {"type": "boolean", "description": "Determines if this is a leaf node(effective when `loadData` is specified), Default: false"}, "a-tree-node/key": {"type": "string | number", "description": "Used with (default)ExpandedKeys / (default)CheckedKeys / (default)SelectedKeys. P.S.: It must be unique in all of treeNodes of the tree!, Default: internal calculated position of treeNode"}, "a-tree-node/selectable": {"type": "boolean", "description": "Set whether the treeNode can be selected, Default: true"}, "a-tree-node/style": {"type": "string|object", "description": "style, Default: -"}, "a-tree-node/title": {"type": "string", "description": "Title, Default: '---'"}, "a-directory-tree/expandAction": {"type": "string", "description": "Directory open logic, optional `false` `'click'` `'dblclick'`, Default: click"}, "a-typography-text/code": {"type": "boolean", "description": "Code style, Default: false"}, "a-typography-text/content(v-model)": {"type": "string", "description": "When using ellipsis or editable, use content instead of children, Default: -"}, "a-typography-text/copyable": {"type": "boolean | [copyable](#copyable)", "description": "Whether to be copyable, customize it via setting an object, Default: false"}, "a-typography-text/delete": {"type": "boolean", "description": "Deleted line style, Default: false"}, "a-typography-text/disabled": {"type": "boolean", "description": "Disabled content, Default: false"}, "a-typography-text/editable": {"type": "boolean | [editable](#editable)", "description": "If editable. Can control edit state when is object, Default: false"}, "a-typography-text/ellipsis": {"type": "boolean", "description": "Display ellipsis when text overflows, Default: false"}, "a-typography-text/keyboard": {"type": "boolean", "description": "Keyboard style, Default: false"}, "a-typography-text/mark": {"type": "boolean", "description": "Marked style, Default: false"}, "a-typography-text/strong": {"type": "boolean", "description": "Bold style, Default: false"}, "a-typography-text/type": {"type": "`secondary` | `success` | `warning` | `danger`", "description": "Content type, Default: -"}, "a-typography-text/underline": {"type": "boolean", "description": "Underlined style, Default: false"}, "a-typography-title/code": {"type": "boolean", "description": "Code style, Default: false"}, "a-typography-title/content(v-model)": {"type": "string", "description": "When using ellipsis or editable, use content instead of children, Default: -"}, "a-typography-title/copyable": {"type": "boolean | [copyable](#copyable)", "description": "Whether to be copyable, customize it via setting an object, Default: false"}, "a-typography-title/delete": {"type": "boolean", "description": "Deleted line style, Default: false"}, "a-typography-title/disabled": {"type": "boolean", "description": "Disabled content, Default: false"}, "a-typography-title/editable": {"type": "boolean | [editable](#editable)", "description": "If editable. Can control edit state when is object, Default: false"}, "a-typography-title/ellipsis": {"type": "boolean | [ellipsis](#ellipsis)", "description": "Display ellipsis when text overflows, can configure rows and expandable by using object, Default: false"}, "a-typography-title/level": {"type": "number: 1, 2, 3, 4, 5", "description": "Set content importance. Match with `h1`, `h2`, `h3`, `h4`, `h5`, Default: 1"}, "a-typography-title/mark": {"type": "boolean", "description": "Marked style, Default: false"}, "a-typography-title/type": {"type": "`secondary` | `success` | `warning` | `danger`", "description": "Content type, Default: -"}, "a-typography-title/underline": {"type": "boolean", "description": "Underlined style, Default: false"}, "a-typography-paragraph/code": {"type": "boolean", "description": "Code style, Default: false"}, "a-typography-paragraph/content(v-model)": {"type": "string", "description": "When using ellipsis or editable, use content instead of children, Default: -"}, "a-typography-paragraph/copyable": {"type": "boolean | [copyable](#copyable)", "description": "Whether to be copyable, customize it via setting an object, Default: false"}, "a-typography-paragraph/delete": {"type": "boolean", "description": "Deleted line style, Default: false"}, "a-typography-paragraph/disabled": {"type": "boolean", "description": "Disabled content, Default: false"}, "a-typography-paragraph/editable": {"type": "boolean | [editable](#editable)", "description": "If editable. Can control edit state when is object, Default: false"}, "a-typography-paragraph/ellipsis": {"type": "boolean | [ellipsis](#ellipsis)", "description": "Display ellipsis when text overflows, can configure rows and expandable by using object, Default: false"}, "a-typography-paragraph/mark": {"type": "boolean", "description": "Marked style, Default: false"}, "a-typography-paragraph/strong": {"type": "boolean", "description": "Bold style, Default: false"}, "a-typography-paragraph/type": {"type": "`secondary` | `success` | `warning` | `danger`", "description": "Content type, Default: -"}, "a-typography-paragraph/underline": {"type": "boolean", "description": "Underlined style, Default: false"}, "a-upload/accept": {"type": "string", "description": "File types that can be accepted. See [input accept Attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#accept), Default: -"}, "a-upload/action": {"type": "string|(file) => `Promise`", "description": "Uploading URL, Default: -"}, "a-upload/beforeUpload": {"type": "(file, fileList) => `boolean` | `Promise`", "description": "Hook function which will be executed before uploading. Uploading will be stopped with `false` or a rejected Promise returned., Default: -"}, "a-upload/customRequest": {"type": "function", "description": "override for the default xhr behavior allowing for additional customization and ability to implement your own XMLHttpRequest, Default: -"}, "a-upload/data": {"type": "object|function(file)", "description": "Uploading params or function which can return uploading params., Default: -"}, "a-upload/directory": {"type": "boolean", "description": "Support upload whole directory（[caniuse](https://caniuse.com/#feat=input-file-directory)）, Default: false"}, "a-upload/disabled": {"type": "boolean", "description": "disable upload button, Default: -"}, "a-upload/downloadIcon": {"type": "v-slot:iconRender=\"{file: UploadFile}\"", "description": "custom download icon, Default: -"}, "a-upload/fileList": {"type": "object[]", "description": "List of files that have been uploaded (controlled). Here is a common issue [#2423](https://github.com/ant-design/ant-design/issues/2423) when using it, Default: -"}, "a-upload/headers": {"type": "object", "description": "Set request headers, valid above IE10., Default: -"}, "a-upload/iconRender": {"type": "v-slot:iconRender=\"{file: UploadFile, listType?: UploadListType}\"", "description": "Custom show icon, Default: -"}, "a-upload/isImageUrl": {"type": "(file: UploadFile) => boolean", "description": "Customize if render &lt;img /> in thumbnail, Default: -"}, "a-upload/itemRender": {"type": "v-slot:itemRender=\"{originNode: VNode, file: UploadFile, fileList: object[], actions: { download: function, preview: function, remove: function }\"", "description": "Custom item of uploadList, Default: -"}, "a-upload/listType": {"type": "string", "description": "Built-in stylesheets, support for three types: `text`, `picture` or `picture-card`, Default: `text`"}, "a-upload/maxCount": {"type": "number", "description": "Limit the number of uploaded files. Will replace current one when `maxCount` is `1`, Default: -"}, "a-upload/method": {"type": "string", "description": "http method of upload request, Default: `post`"}, "a-upload/multiple": {"type": "boolean", "description": "Whether to support selected multiple file. `IE10+` supported. You can select multiple files with CTRL holding down while multiple is set to be true, Default: false"}, "a-upload/name": {"type": "string", "description": "The name of uploading file, Default: `file`"}, "a-upload/openFileDialogOnClick": {"type": "boolean", "description": "Click open file dialog, Default: true"}, "a-upload/previewFile": {"type": "(file: File | Blob) => Promise&lt;dataURL: string>", "description": "Customize preview file logic, Default: -"}, "a-upload/previewIcon": {"type": "v-slot:iconRender=\"{file: UploadFile}\"", "description": "custom preview icon, Default: -"}, "a-upload/progress": {"type": "[ProgressProps](/components/progress/#api) (support `type=\"line\"` only)", "description": "Custom progress bar, Default: { strokeWidth: 2, showInfo: false }"}, "a-upload/removeIcon": {"type": "v-slot:iconRender=\"{file: UploadFile}\"", "description": "custom remove icon, Default: -"}, "a-upload/showUploadList": {"type": "boolean | { showPreviewIcon?: boolean, showRemoveIcon?: boolean, showDownloadIcon?: boolean }", "description": "Whether to show default upload list, could be an object to specify `showPreviewIcon`, `showRemoveIcon` and `showDownloadIcon` individually, Default: true"}, "a-upload/supportServerRender": {"type": "boolean", "description": "Need to be turned on while the server side is rendering., Default: false"}, "a-upload/withCredentials": {"type": "boolean", "description": "ajax upload with cookie sent, Default: false"}, "a-upload-file/crossOrigin": {"type": "`'anonymous'` | `'use-credentials'` | `''`", "description": "CORS settings attributes, Default: -"}, "a-upload-file/name": {"type": "string", "description": "File name, Default: -"}, "a-upload-file/percent": {"type": "number", "description": "Upload progress percent, Default: -"}, "a-upload-file/status": {"type": "`error` | `success` | `done` | `uploading` | `removed`", "description": "Upload status. Show different style when configured, Default: -"}, "a-upload-file/thumbUrl": {"type": "string", "description": "Thumb image url, De<PERSON>ult: -"}, "a-upload-file/uid": {"type": "string", "description": "unique id. Will auto generate when not provided, Default: -"}, "a-upload-file/url": {"type": "string", "description": "Download url, De<PERSON>ult: -"}, "a-watermark/width": {"type": "number", "description": "The width of the watermark, the default value of `content` is its own width, Default: 120"}, "a-watermark/height": {"type": "number", "description": "The height of the watermark, the default value of `content` is its own height, Default: 64"}, "a-watermark/rotate": {"type": "number", "description": "When the watermark is drawn, the rotation Angle, unit `°`, Default: -22"}, "a-watermark/zIndex": {"type": "number", "description": "The z-index of the appended watermark element, Default: 9"}, "a-watermark/image": {"type": "string", "description": "Image source, it is recommended to export 2x or 3x image, high priority, Default: -"}, "a-watermark/content": {"type": "string | string[]", "description": "Watermark text content, Default: -"}, "a-watermark/font": {"type": "[Font](#font)", "description": "Text style, Default: [Font](#font)"}, "a-watermark/gap": {"type": "[number, number]", "description": "The spacing between watermarks, Default: \\[100, 100\\]"}, "a-watermark/offset": {"type": "[number, number]", "description": "The offset of the watermark from the upper left corner of the container. The default is `gap/2`, Default: \\[gap\\[0\\]/2, gap\\[1\\]/2\\]"}}