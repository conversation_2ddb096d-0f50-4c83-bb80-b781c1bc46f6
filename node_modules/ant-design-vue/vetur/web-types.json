{"$schema": "https://raw.githubusercontent.com/JetBrains/web-types/master/schema/web-types.json", "framework": "vue", "name": "ant-design-vue", "version": "4.2.6", "contributions": {"html": {"tags": [{"name": "a-anchor", "slots": [{"name": "customTitle", "description": "custom option title by slot"}], "events": [], "attributes": [{"name": "affix", "default": "true", "description": "Fixed mode of Anchor", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bounds", "default": "5(px)", "description": "Bounding distance of anchor area", "value": {"type": "number", "kind": "expression"}}, {"name": "getContainer", "default": "() => window", "description": "Scrolling container", "value": {"type": "() => HTMLElement", "kind": "expression"}}, {"name": "getCurrentAnchor", "default": "-", "description": "Customize the anchor highlight", "value": {"type": "(activeLink: string) => string", "kind": "expression"}}, {"name": "offsetBottom", "default": "-", "description": "Pixels to offset from bottom when calculating position of scroll", "value": {"type": "number", "kind": "expression"}}, {"name": "offsetTop", "default": "0", "description": "Pixels to offset from top when calculating position of scroll", "value": {"type": "number", "kind": "expression"}}, {"name": "showInkInFixed", "default": "false", "description": "Whether show ink-square when `：affix=\"false\"`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "targetOffset", "default": "`offsetTop`", "description": "Anchor scroll offset, default as `offsetTop`, [example](#components-anchor-demo-targetoffset)", "value": {"type": "number", "kind": "expression"}}, {"name": "wrapperClass", "default": "-", "description": "The class name of the container", "value": {"type": "string", "kind": "expression"}}, {"name": "wrapperStyle", "default": "-", "description": "The style of the container", "value": {"type": "object", "kind": "expression"}}, {"name": "items", "default": "-", "description": "Data configuration option content, support nesting through children", "value": {"type": "{ key, href, title, target, children }[] [see](#anchoritem)", "kind": "expression"}}, {"name": "direction", "default": "`vertical`", "description": "Set Anchor direction", "value": {"type": "`vertical` | `horizontal`", "kind": "expression"}}, {"name": "customTitle", "default": "-", "description": "custom option title by slot", "value": {"type": "v-slot=\"AnchorItem\"", "kind": "expression"}}]}, {"name": "a-anchor-item", "slots": [], "events": [], "attributes": [{"name": "key", "default": "-", "description": "The unique identifier of the Anchor Link", "value": {"type": "string | number", "kind": "expression"}}, {"name": "href", "default": "", "description": "The target of hyperlink", "value": {"type": "string", "kind": "expression"}}, {"name": "target", "default": "", "description": "Specifies where to display the linked URL", "value": {"type": "string", "kind": "expression"}}, {"name": "title", "default": "", "description": "The content of hyperlink", "value": {"type": "VueNode | (item: AnchorItem) => VueNode", "kind": "expression"}}, {"name": "children", "default": "-", "description": "Nested Anchor Link, `Attention: This attribute does not support horizontal orientation`", "value": {"type": "[AnchorItem](#anchoritem)[]", "kind": "expression"}}]}, {"name": "a-affix", "slots": [], "events": [{"name": "change", "description": "Callback for when Affix state is changed"}], "attributes": [{"name": "offsetBottom", "default": "-", "description": "Offset from the bottom of the viewport (in pixels)", "value": {"type": "number", "kind": "expression"}}, {"name": "offsetTop", "default": "0", "description": "Offset from the top of the viewport (in pixels)", "value": {"type": "number", "kind": "expression"}}, {"name": "target", "default": "() => window", "description": "Specifies the scrollable area DOM node", "value": {"type": "() => HTMLElement", "kind": "expression"}}]}, {"name": "a-alert", "slots": [{"name": "action", "description": "The action of <PERSON><PERSON>"}, {"name": "closeIcon", "description": "Custom close icon"}, {"name": "closeText", "description": "Close text to show"}, {"name": "description", "description": "Additional content of Al<PERSON>"}, {"name": "icon", "description": "Custom icon, effective when `showIcon` is `true`"}, {"name": "message", "description": "Content of Alert"}], "events": [{"name": "close", "description": "Callback when <PERSON><PERSON> is closed"}], "attributes": [{"name": "action", "default": "-", "description": "The action of <PERSON><PERSON>", "value": {"type": "slot", "kind": "expression"}}, {"name": "afterClose", "default": "-", "description": "Called when close animation is finished", "value": {"type": "() => void", "kind": "expression"}}, {"name": "banner", "default": "false", "description": "Whether to show as banner", "value": {"type": "boolean", "kind": "expression"}}, {"name": "closable", "default": "", "description": "Whether Alert can be closed", "value": {"type": "boolean", "kind": "expression"}}, {"name": "closeIcon", "default": "`<CloseOutlined />`", "description": "Custom close icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "closeText", "default": "-", "description": "Close text to show", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "description", "default": "-", "description": "Additional content of Al<PERSON>", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "Custom icon, effective when `showIcon` is `true`", "value": {"type": "vnode | slot", "kind": "expression"}}, {"name": "message", "default": "-", "description": "Content of Alert", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "showIcon", "default": "false,in `banner` mode default is true", "description": "Whether to show icon", "value": {"type": "boolean", "kind": "expression"}}, {"name": "type", "default": "`info`,in `banner` mode default is `warning`", "description": "Type of Alert styles, options: `success`, `info`, `warning`, `error`", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-app", "slots": [], "events": [], "attributes": [{"name": "message", "default": "-", "description": "Global config for Message", "value": {"type": "[MessageConfig](/components/message/#messageconfig)", "kind": "expression"}}, {"name": "notification", "default": "-", "description": "Global config for Notification", "value": {"type": "[NotificationConfig](/components/notification/#notificationconfig)", "kind": "expression"}}]}, {"name": "a-auto-complete", "slots": [{"name": "clearIcon", "description": "Use slot custom clear icon"}, {"name": "default (for customize input element)", "description": "customize input element"}, {"name": "option", "description": "custom render option by slot"}], "events": [{"name": "blur", "description": "Called when leaving the component."}, {"name": "change", "description": "Called when select an option or input value change, or value of input is changed"}, {"name": "dropdownVisibleChange", "description": "Call when dropdown open"}, {"name": "focus", "description": "Called when entering the component"}, {"name": "search", "description": "Called when searching items."}, {"name": "select", "description": "Called when a option is selected. param is option's value and option instance."}, {"name": "clear", "description": "Called when clear"}], "attributes": [{"name": "allowClear", "default": "false", "description": "Show clear button, effective in multiple mode only.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "autofocus", "default": "false", "description": "get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "backfill", "default": "false", "description": "backfill selected item the input when using keyboard", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bordered", "default": "true", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clearIcon", "default": "`<CloseCircleFilled />`", "description": "Use slot custom clear icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "default (for customize input element)", "default": "`<Input />`", "description": "customize input element", "value": {"type": "slot", "kind": "expression"}}, {"name": "defaultActiveFirstOption", "default": "true", "description": "Whether active first option by default", "value": {"type": "boolean", "kind": "expression"}}, {"name": "defaultOpen", "default": "-", "description": "Initial open state of dropdown", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Whether disabled select", "value": {"type": "boolean", "kind": "expression"}}, {"name": "popupClassName", "default": "-", "description": "The className of dropdown menu", "value": {"type": "string", "kind": "expression"}}, {"name": "dropdownMatchSelectWidth", "default": "true", "description": "Determine whether the dropdown menu and the select input are the same width. <PERSON><PERSON><PERSON> set `min-width` same as input. Will ignore when value less than select width. `false` will disable virtual scroll", "value": {"type": "boolean | number", "kind": "expression"}}, {"name": "dropdownMenuStyle", "default": "", "description": "additional style applied to dropdown menu", "value": {"type": "object", "kind": "expression"}}, {"name": "filterOption", "default": "true", "description": "If true, filter options by input, if function, filter options against it. The function will receive two arguments, `inputValue` and `option`, if the function returns `true`, the option will be included in the filtered set; Otherwise, it will be excluded.", "value": {"type": "boolean or function(inputValue, option)", "kind": "expression"}}, {"name": "open", "default": "-", "description": "Controlled open state of dropdown", "value": {"type": "boolean", "kind": "expression"}}, {"name": "option", "default": "-", "description": "custom render option by slot", "value": {"type": "v-slot:option=\"{value, label, [disabled, key, title]}\"", "kind": "expression"}}, {"name": "options", "default": "", "description": "Data source for autocomplete", "value": {"type": "[DataSourceItemType](https://github.com/vueComponent/ant-design-vue/blob/724d53b907e577cf5880c1e6742d4c3f924f8f49/components/auto-complete/index.vue#L9)[]", "kind": "expression"}}, {"name": "placeholder", "default": "-", "description": "placeholder of input", "value": {"type": "string", "kind": "expression"}}, {"name": "status", "default": "-", "description": "Set validation status", "value": {"type": "'error' | 'warning'", "kind": "expression"}}, {"name": "v-model:value", "default": "-", "description": "selected option", "value": {"type": "string|string[]|{ key: string, label: string|vNodes }|Array&lt;{ key: string, label: string|vNodes }>", "kind": "expression"}}]}, {"name": "a-avatar", "slots": [{"name": "icon", "description": "the `Icon` type for an icon avatar, see `Icon` Component"}], "events": [], "attributes": [{"name": "alt", "default": "-", "description": "This attribute defines the alternative text describing the image", "value": {"type": "string", "kind": "expression"}}, {"name": "crossOrigin", "default": "-", "description": "cors settings attributes", "value": {"type": "`'anonymous'` | `'use-credentials'` | `''`", "kind": "expression"}}, {"name": "draggable", "default": "-", "description": "Whether the picture is allowed to be dragged", "value": {"type": "boolean | `'true'` | `'false'`", "kind": "expression"}}, {"name": "gap", "default": "4", "description": "Letter type unit distance between left and right sides", "value": {"type": "number", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "the `Icon` type for an icon avatar, see `Icon` Component", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "loadError", "default": "-", "description": "handler when img load error, return false to prevent default fallback behavior", "value": {"type": "() => boolean", "kind": "expression"}}, {"name": "shape", "default": "`circle`", "description": "the shape of avatar", "value": {"type": "`circle` | `square`", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "The size of the avatar", "value": {"type": "number | `large` | `small` | `default` | { xs: number, sm: number, ...}", "kind": "expression"}}, {"name": "src", "default": "-", "description": "the address of the image for an image avatar", "value": {"type": "string", "kind": "expression"}}, {"name": "srcset", "default": "-", "description": "a list of sources to use for different screen resolutions", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-avatar-group", "slots": [], "events": [], "attributes": [{"name": "maxCount", "default": "-", "description": "Max avatars to show", "value": {"type": "number", "kind": "expression"}}, {"name": "maxPopoverPlacement", "default": "`top`", "description": "The placement of excess avatar Pop<PERSON>", "value": {"type": "`top` | `bottom`", "kind": "expression"}}, {"name": "maxPopoverTrigger", "default": "`hover`", "description": "Set the trigger of excess avatar Pop<PERSON>", "value": {"type": "`hover` | `focus` | `click`", "kind": "expression"}}, {"name": "maxStyle", "default": "-", "description": "The style of excess avatar style", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "The size of the avatar", "value": {"type": "number | `large` | `small` | `default` | { xs: number, sm: number, ...}", "kind": "expression"}}, {"name": "shape", "default": "`circle`", "description": "The shape of the avatar", "value": {"type": "`circle` | `square`", "kind": "expression"}}]}, {"name": "a-breadcrumb", "slots": [{"name": "separator", "description": "Custom separator"}], "events": [], "attributes": [{"name": "itemRender", "default": "", "description": "Custom item renderer, #itemRender=\"{route, params, routes, paths}\"", "value": {"type": "({route, params, routes, paths}) => vNode", "kind": "expression"}}, {"name": "params", "default": "", "description": "Routing parameters", "value": {"type": "object", "kind": "expression"}}, {"name": "routes", "default": "", "description": "The routing stack information of router", "value": {"type": "[routes[]](#routes)", "kind": "expression"}}, {"name": "separator", "default": "", "description": "Custom separator", "value": {"type": "string|slot", "kind": "expression"}}]}, {"name": "a-breadcrumb-item", "slots": [], "events": [], "attributes": [{"name": "href", "default": "-", "description": "Target of hyperlink", "value": {"type": "string", "kind": "expression"}}, {"name": "overlay", "default": "-", "description": "The dropdown menu", "value": {"type": "[Menu](/components/menu) | () => Menu", "kind": "expression"}}]}, {"name": "a-breadcrumb-separator", "slots": [], "events": [], "attributes": []}, {"name": "a-button", "slots": [{"name": "icon", "description": "set the icon of button, see: Icon component"}], "events": [{"name": "click", "description": "set the handler to handle `click` event"}], "attributes": [{"name": "block", "default": "`false`", "description": "option to fit button width to its parent width", "value": {"type": "boolean", "kind": "expression"}}, {"name": "danger", "default": "`false`", "description": "set the danger status of button", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "disabled state of button", "value": {"type": "boolean", "kind": "expression"}}, {"name": "ghost", "default": "`false`", "description": "make background transparent and invert text and border colors", "value": {"type": "boolean", "kind": "expression"}}, {"name": "href", "default": "-", "description": "redirect url of link button", "value": {"type": "string", "kind": "expression"}}, {"name": "htmlType", "default": "`button`", "description": "set the original html `type` of `button`, see: [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/button#attr-type)", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "set the icon of button, see: Icon component", "value": {"type": "v-slot", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "set the loading status of button", "value": {"type": "boolean | { delay: number }", "kind": "expression"}}, {"name": "shape", "default": "`default`", "description": "Can be set button shape", "value": {"type": "`default` | `circle` | `round`", "kind": "expression"}}, {"name": "size", "default": "`middle`", "description": "set the size of button", "value": {"type": "`large` | `middle` | `small`", "kind": "expression"}}, {"name": "target", "default": "-", "description": "same as target attribute of a, works when href is specified", "value": {"type": "string", "kind": "expression"}}, {"name": "type", "default": "`default`", "description": "can be set button type", "value": {"type": "`primary` | `ghost` | `dashed` | `link` | `text` | `default`", "kind": "expression"}}]}, {"name": "a-calendar", "slots": [{"name": "date<PERSON>ell<PERSON><PERSON>", "description": "Customize the display of the date cell by setting a scoped slot, the returned content will be appended to the cell"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Customize the display of the date cell by setting a scoped slot, the returned content will override the cell"}, {"name": "headerRender", "description": "render custom header in panel"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Customize the display of the month cell by setting a scoped slot, the returned content will be appended to the cell"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Customize the display of the month cell by setting a scoped slot, the returned content will override the cell"}], "events": [{"name": "change", "description": "Callback for when value change"}, {"name": "panelChange", "description": "Callback for when panel changes"}, {"name": "select", "description": "Callback for when a date is selected, include source info"}], "attributes": [{"name": "date<PERSON>ell<PERSON><PERSON>", "default": "-", "description": "Customize the display of the date cell by setting a scoped slot, the returned content will be appended to the cell", "value": {"type": "v-slot:dateCellRender=\"{current: dayjs}\"", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "-", "description": "Customize the display of the date cell by setting a scoped slot, the returned content will override the cell", "value": {"type": "v-slot:dateFullCellRender=\"{current: dayjs}\"", "kind": "expression"}}, {"name": "disabledDate", "default": "-", "description": "Function that specifies the dates that cannot be selected", "value": {"type": "(currentDate: dayjs) => boolean", "kind": "expression"}}, {"name": "fullscreen", "default": "`true`", "description": "Whether to display in full-screen", "value": {"type": "boolean", "kind": "expression"}}, {"name": "headerRender", "default": "-", "description": "render custom header in panel", "value": {"type": "v-slot:headerRender=\"{value: dayjs, type: string, onChange: f(), onTypeChange: f()}\"", "kind": "expression"}}, {"name": "locale", "default": "[default](https://github.com/vueComponent/ant-design-vue/blob/main/components/date-picker/locale/example.json)", "description": "The calendar's locale", "value": {"type": "object", "kind": "expression"}}, {"name": "mode", "default": "`month`", "description": "The display mode of the calendar", "value": {"type": "`month` | `year`", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "-", "description": "Customize the display of the month cell by setting a scoped slot, the returned content will be appended to the cell", "value": {"type": "v-slot:monthCellRender=\"{current: dayjs}\"", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "-", "description": "Customize the display of the month cell by setting a scoped slot, the returned content will override the cell", "value": {"type": "v-slot:monthFull<PERSON>ellRender=\"{current: dayjs}\"", "kind": "expression"}}, {"name": "validRange", "default": "-", "description": "to set valid range", "value": {"type": "[[dayjs](https://day.js.org/), [dayjs](https://day.js.org/)]", "kind": "expression"}}, {"name": "value(v-model)", "default": "current date", "description": "The current selected date", "value": {"type": "[dayjs](https://day.js.org/)", "kind": "expression"}}, {"name": "valueFormat", "default": "-", "description": "optional, format of binding value. If not specified, the binding value will be a Date object", "value": {"type": "string, [date formats](https://day.js.org/docs/en/display/format)", "kind": "expression"}}]}, {"name": "a-card", "slots": [{"name": "extra", "description": "Content to render in the top-right corner of the card"}, {"name": "title", "description": "Card title"}], "events": [{"name": "tabChange", "description": "Callback when tab is switched"}], "attributes": [{"name": "activeTabKey", "default": "-", "description": "Current TabPane's key", "value": {"type": "string", "kind": "expression"}}, {"name": "bodyStyle", "default": "-", "description": "Inline style to apply to the card content", "value": {"type": "object", "kind": "expression"}}, {"name": "bordered", "default": "`true`", "description": "Toggles rendering of the border around the card", "value": {"type": "boolean", "kind": "expression"}}, {"name": "defaultActiveTabKey", "default": "-", "description": "Initial active TabPane's key, if `activeTabKey` is not set.", "value": {"type": "string", "kind": "expression"}}, {"name": "extra", "default": "-", "description": "Content to render in the top-right corner of the card", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "headStyle", "default": "-", "description": "Inline style to apply to the card head", "value": {"type": "object", "kind": "expression"}}, {"name": "hoverable", "default": "false", "description": "Lift up when hovering card", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loading", "default": "false", "description": "Shows a loading indicator while the contents of the card are being fetched", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "Size of card", "value": {"type": "`default` | `small`", "kind": "expression"}}, {"name": "tabList", "default": "-", "description": "List of TabPane's head, Custom tabs with the customTab(v3.0) slot", "value": {"type": "Array&lt;{key: string, tab: any}>", "kind": "expression"}}, {"name": "title", "default": "-", "description": "Card title", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "type", "default": "-", "description": "Card style type, can be set to `inner` or not set", "value": {"type": "string", "kind": "expression"}}, {"name": "actions", "default": "", "description": "The action list, shows at the bottom of the Card.", "value": {"type": "-", "kind": "expression"}}, {"name": "cover", "default": "", "description": "Card cover", "value": {"type": "-", "kind": "expression"}}, {"name": "customTab", "default": "", "description": "custom tabList tab", "value": {"type": "{ item: tabList[number] }", "kind": "expression"}}, {"name": "extra", "default": "", "description": "Content to render in the top-right corner of the card", "value": {"type": "-", "kind": "expression"}}, {"name": "tabBarExtraContent", "default": "", "description": "Extra content in tab bar", "value": {"type": "-", "kind": "expression"}}, {"name": "title", "default": "", "description": "Card title", "value": {"type": "-", "kind": "expression"}}]}, {"name": "a-card-meta", "slots": [{"name": "avatar", "description": "avatar or icon"}, {"name": "description", "description": "description content"}, {"name": "title", "description": "title content"}], "events": [], "attributes": [{"name": "avatar", "default": "-", "description": "avatar or icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "description", "default": "-", "description": "description content", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "title", "default": "-", "description": "title content", "value": {"type": "string|slot", "kind": "expression"}}]}, {"name": "a-carousel", "slots": [], "events": [], "attributes": [{"name": "autoplay", "default": "`false`", "description": "Whether to scroll automatically", "value": {"type": "boolean", "kind": "expression"}}, {"name": "dotPosition", "default": "`bottom`", "description": "The position of the dots, which can be one of `top` `bottom` `left` `right`", "value": {"type": "string", "kind": "expression"}}, {"name": "dots", "default": "`true`", "description": "Whether to show the dots at the bottom of the gallery", "value": {"type": "boolean", "kind": "expression"}}, {"name": "dotsClass", "default": "`slick-dots`", "description": "Class name of the dots", "value": {"type": "string", "kind": "expression"}}, {"name": "easing", "default": "`linear`", "description": "Transition interpolation function name", "value": {"type": "string", "kind": "expression"}}, {"name": "effect", "default": "`scrollx`", "description": "Transition effect", "value": {"type": "`scrollx` | `fade`", "kind": "expression"}}, {"name": "afterChange", "default": "-", "description": "Callback function called after the current index changes", "value": {"type": "function(current)", "kind": "expression"}}, {"name": "beforeChange", "default": "-", "description": "Callback function called before the current index changes", "value": {"type": "function(from, to)", "kind": "expression"}}]}, {"name": "a-cascader", "slots": [{"name": "clearIcon", "description": "The custom clear icon"}, {"name": "expandIcon", "description": "Customize the current item expand icon"}, {"name": "maxTagPlaceholder", "description": "Placeholder for not showing tags"}, {"name": "notFoundContent", "description": "Specify content to show when no result matches."}, {"name": "removeIcon", "description": "The custom remove icon"}, {"name": "suffixIcon", "description": "The custom suffix icon"}, {"name": "tagRender", "description": "Customize tag render when `multiple`"}], "events": [{"name": "change", "description": "callback when finishing cascader select"}, {"name": "dropdownVisibleChange", "description": "callback when popup shown or hidden"}, {"name": "search", "description": "callback when input value change"}], "attributes": [{"name": "allowClear", "default": "true", "description": "whether allow clear", "value": {"type": "boolean", "kind": "expression"}}, {"name": "autofocus", "default": "false", "description": "get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bordered", "default": "true", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clearIcon", "default": "-", "description": "The custom clear icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "changeOnSelect", "default": "false", "description": "(Work on single select) change value on each selection if set to true, see above demo for details", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "whether disabled select", "value": {"type": "boolean", "kind": "expression"}}, {"name": "displayRender", "default": "`labels => labels.join(' / ')`", "description": "render function of displaying selected options, you can use #displayRender=\"{labels, selectedOptions}\".", "value": {"type": "`({labels, selectedOptions}) => VNode`", "kind": "expression"}}, {"name": "popupClassName", "default": "-", "description": "additional className of popup overlay", "value": {"type": "string", "kind": "expression"}}, {"name": "dropdownStyle", "default": "{}", "description": "additional style of popup overlay", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "expandIcon", "default": "-", "description": "Customize the current item expand icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "expandTrigger", "default": "'click'", "description": "expand current item when click or hover", "value": {"type": "`click` | `hover`", "kind": "expression"}}, {"name": "fieldNames", "default": "`{ label: 'label', value: 'value', children: 'children' }`", "description": "custom field name for label and value and children", "value": {"type": "object", "kind": "expression"}}, {"name": "getPopupContainer", "default": "() => document.body", "description": "Parent Node which the selector should be rendered to. Default to `body`. When position issues happen, try to modify it into scrollable content and position it relative.", "value": {"type": "Function(triggerNode)", "kind": "expression"}}, {"name": "loadData", "default": "-", "description": "To load option lazily, and it cannot work with `showSearch`", "value": {"type": "`(selectedOptions) => void`", "kind": "expression"}}, {"name": "maxTag<PERSON>ount", "default": "-", "description": "Max tag count to show. `responsive` will cost render performance", "value": {"type": "number | `responsive`", "kind": "expression"}}, {"name": "maxTagPlaceholder", "default": "-", "description": "Placeholder for not showing tags", "value": {"type": "v-slot | function(omittedValues)", "kind": "expression"}}, {"name": "multiple", "default": "-", "description": "Support multiple or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "notFoundContent", "default": "'Not Found'", "description": "Specify content to show when no result matches.", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "open", "default": "-", "description": "set visible of cascader popup", "value": {"type": "boolean", "kind": "expression"}}, {"name": "options", "default": "-", "description": "data options of cascade", "value": {"type": "[Option](#option)[]", "kind": "expression"}}, {"name": "placeholder", "default": "'Please select'", "description": "input placeholder", "value": {"type": "string", "kind": "expression"}}, {"name": "placement", "default": "`bottomLeft`", "description": "Use preset popup align config from builtinPlacements", "value": {"type": "`bottomLeft` | `bottomRight` | `topLeft` | `topRight`", "kind": "expression"}}, {"name": "removeIcon", "default": "-", "description": "The custom remove icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "searchValue", "default": "-", "description": "Set search value, Need work with `showSearch`", "value": {"type": "string", "kind": "expression"}}, {"name": "showSearch", "default": "false", "description": "Whether show search input in single mode.", "value": {"type": "boolean | [object](#showsearch)", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "input size", "value": {"type": "`large` | `default` | `small`", "kind": "expression"}}, {"name": "status", "default": "-", "description": "Set validation status", "value": {"type": "'error' | 'warning'", "kind": "expression"}}, {"name": "suffixIcon", "default": "-", "description": "The custom suffix icon", "value": {"type": "string | VNode | slot", "kind": "expression"}}, {"name": "showCheckedStrategy", "default": "`Cascader.SHOW_PARENT`", "description": "The way show selected item in box. ** `SHOW_CHILD`: ** just show child treeNode. **`Cascader.SHOW_PARENT`:** just show parent treeNode (when all child treeNode under the parent treeNode are checked)", "value": {"type": "`Cascader.SHOW_PARENT` | `Cascader.SHOW_CHILD`", "kind": "expression"}}, {"name": "tagRender", "default": "-", "description": "Customize tag render when `multiple`", "value": {"type": "slot", "kind": "expression"}}, {"name": "value(v-model)", "default": "-", "description": "selected value", "value": {"type": "string[] | number[]", "kind": "expression"}}]}, {"name": "a-checkbox", "slots": [], "events": [{"name": "change", "description": "The callback function that is triggered when the state changes."}, {"name": "change", "description": "The callback function that is triggered when the state changes."}], "attributes": [{"name": "autofocus", "default": "false", "description": "get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "checked(v-model)", "default": "false", "description": "Specifies whether the checkbox is selected.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disable checkbox", "value": {"type": "boolean", "kind": "expression"}}, {"name": "indeterminate", "default": "false", "description": "indeterminate checked state of checkbox", "value": {"type": "boolean", "kind": "expression"}}, {"name": "value", "default": "-", "description": "value of checkbox in CheckboxGroup", "value": {"type": "boolean | string | number", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disable all checkboxes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "name", "default": "-", "description": "The `name` property of all `input[type=\"checkbox\"]` children", "value": {"type": "string", "kind": "expression"}}, {"name": "options", "default": "\\[]", "description": "Specifies options, you can customize `label` with slot = \"label\" slot-scope=\"option\"", "value": {"type": "string[] | Array&lt;{ label: string value: string disabled?: boolean, indeterminate?: boolean, onChange?: function }>", "kind": "expression"}}, {"name": "value(v-model)", "default": "\\[]", "description": "Used for setting the currently selected value.", "value": {"type": "(boolean | string | number)[]", "kind": "expression"}}, {"name": "blur()", "description": "remove focus", "value": {"type": "", "kind": "expression"}}, {"name": "focus()", "description": "get focus", "value": {"type": "", "kind": "expression"}}]}, {"name": "a-collapse", "slots": [{"name": "expandIcon", "description": "allow to customize collapse icon"}], "events": [{"name": "change", "description": "Callback function executed when active panel is changed"}], "attributes": [{"name": "accordion", "default": "`false`", "description": "If `true`, `Collapse` renders as `Accordion`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON>(v-model)", "default": "No default value. In [accordion mode](#components-collapse-demo-accordion), it's the key of the first panel.", "description": "Key of the active panel", "value": {"type": "string[] | string <br> number[] | number", "kind": "expression"}}, {"name": "bordered", "default": "`true`", "description": "Toggles rendering of the border around the collapse block", "value": {"type": "boolean", "kind": "expression"}}, {"name": "collapsible", "default": "-", "description": "Specify whether the panels of children be collapsible or the trigger area of collapsible", "value": {"type": "`header` | `icon` | `disabled`", "kind": "expression"}}, {"name": "destroyInactivePanel", "default": "`false`", "description": "Destroy Inactive Panel", "value": {"type": "boolean", "kind": "expression"}}, {"name": "expandIcon", "default": "", "description": "allow to customize collapse icon", "value": {"type": "Function(props):VNode | v-slot:expandIcon=\"props\"", "kind": "expression"}}, {"name": "expandIconPosition", "default": "-", "description": "Set expand icon position", "value": {"type": "`start` | `end`", "kind": "expression"}}, {"name": "ghost", "default": "false", "description": "Make the collapse borderless and its background transparent", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-collapse-panel", "slots": [{"name": "extra", "description": "extra element in the corner"}, {"name": "header", "description": "Title of the panel"}], "events": [], "attributes": [{"name": "collapsible", "default": "-", "description": "Specify whether the panel be collapsible or the trigger area of collapsible", "value": {"type": "`header` | `disabled`", "kind": "expression"}}, {"name": "disabled", "default": "`false`", "description": "If `true`, panel cannot be opened or closed", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra", "default": "-", "description": "extra element in the corner", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "forceRender", "default": "`false`", "description": "Forced render of content on panel, instead of lazy rending after clicking on header", "value": {"type": "boolean", "kind": "expression"}}, {"name": "header", "default": "-", "description": "Title of the panel", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "key", "default": "-", "description": "Unique key identifying the panel from among its siblings", "value": {"type": "string | number", "kind": "expression"}}, {"name": "showArrow", "default": "`true`", "description": "If `false`, panel will not show arrow icon", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-comment", "slots": [{"name": "actions", "description": "List of action items rendered below the comment content"}, {"name": "author", "description": "The element to display as the comment author"}, {"name": "avatar", "description": "The element to display as the comment avatar - generally an antd `Avatar` or src"}, {"name": "content", "description": "The main content of the comment"}, {"name": "datetime", "description": "A datetime element containing the time to be displayed"}], "events": [], "attributes": [{"name": "actions", "default": "-", "description": "List of action items rendered below the comment content", "value": {"type": "Array | slot", "kind": "expression"}}, {"name": "author", "default": "-", "description": "The element to display as the comment author", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "avatar", "default": "-", "description": "The element to display as the comment avatar - generally an antd `Avatar` or src", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "content", "default": "-", "description": "The main content of the comment", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "datetime", "default": "-", "description": "A datetime element containing the time to be displayed", "value": {"type": "string|slot", "kind": "expression"}}]}, {"name": "a-config-provider", "slots": [{"name": "renderEmpty", "description": "set empty content of components. Ref [Empty](/components/empty/)"}], "events": [], "attributes": [{"name": "autoInsertSpaceInButton", "default": "true", "description": "Set `false` to remove space between 2 chinese characters on Button", "value": {"type": "boolean", "kind": "expression"}}, {"name": "componentSize", "default": "-", "description": "Config antd component size", "value": {"type": "`small` | `middle` | `large`", "kind": "expression"}}, {"name": "csp", "default": "-", "description": "Set [Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP) config", "value": {"type": "{ nonce: string }", "kind": "expression"}}, {"name": "direction", "default": "`ltr`", "description": "Set direction of layout. See [demo](#components-config-provider-demo-direction)", "value": {"type": "`ltr` | `rtl`", "kind": "expression"}}, {"name": "dropdownMatchSelectWidth", "default": "-", "description": "Determine whether the dropdown menu and the select input are the same width. <PERSON><PERSON><PERSON> set `min-width` same as input. Will ignore when value less than select width. `false` will disable virtual scroll", "value": {"type": "boolean | number", "kind": "expression"}}, {"name": "form", "default": "-", "description": "Set Form common props", "value": {"type": "{ validateMessages?: [ValidateMessages](/components/form/#validatemessages), requiredMark?: boolean | `optional` }", "kind": "expression"}}, {"name": "getPopupContainer", "default": "`() => document.body`", "description": "to set the container of the popup element. The default is to create a `div` element in `body`.", "value": {"type": "Function(triggerNode, dialogContext)", "kind": "expression"}}, {"name": "getTargetContainer", "default": "() => window", "description": "Config Affix, Anchor scroll target container", "value": {"type": "() => HTMLElement", "kind": "expression"}}, {"name": "input", "default": "-", "description": "Set Input common props", "value": {"type": "{ autocomplete?: string }", "kind": "expression"}}, {"name": "locale", "default": "-", "description": "language package setting, you can find the packages in [ant-design-vue/es/locale](http://unpkg.com/ant-design-vue/es/locale/)", "value": {"type": "object", "kind": "expression"}}, {"name": "pageHeader", "default": "'true'", "description": "Unify the ghost of pageHeader ,Ref [pageHeader]\\(&lt;(/components/page-header)>", "value": {"type": "{ ghost:boolean }", "kind": "expression"}}, {"name": "prefixCls", "default": "ant", "description": "set prefix class", "value": {"type": "string", "kind": "expression"}}, {"name": "renderEmpty", "default": "-", "description": "set empty content of components. Ref [Empty](/components/empty/)", "value": {"type": "slot-scope | Function(componentName: string): VNode", "kind": "expression"}}, {"name": "space", "default": "-", "description": "Set Space `size`, ref [Space](/components/space)", "value": {"type": "{ size: `small` | `middle` | `large` | `number` }", "kind": "expression"}}, {"name": "transformCellText", "default": "-", "description": "Table data can be changed again before rendering. The default configuration of general user empty data.", "value": {"type": "Function({ text, column, record, index }) => any", "kind": "expression"}}, {"name": "virtual", "default": "true", "description": "Disable virtual scroll when set to false", "value": {"type": "boolean", "kind": "expression"}}, {"name": "wave", "default": "-", "description": "Config wave effect", "value": {"type": "{ disabled?: boolean }", "kind": "expression"}}]}, {"name": "a-date-picker", "slots": [{"name": "dateRender", "description": "Custom rendering function for date cells"}, {"name": "nextIcon", "description": "The custom next icon"}, {"name": "presets", "description": "The preset ranges for quick selection"}, {"name": "prevIcon", "description": "The custom prev icon"}, {"name": "suffixIcon", "description": "The custom suffix icon"}, {"name": "superNextIcon", "description": "The custom super next icon"}, {"name": "superPrevIcon", "description": "The custom super prev icon"}], "events": [], "attributes": [{"name": "allowClear", "default": "true", "description": "Whether to show clear button", "value": {"type": "boolean", "kind": "expression"}}, {"name": "autofocus", "default": "false", "description": "If get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bordered", "default": "true", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "dateRender", "default": "-", "description": "Custom rendering function for date cells", "value": {"type": "v-slot:dateRender=\"{current, today}\"", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Determine whether the DatePicker is disabled", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabledDate", "default": "-", "description": "Specify the date that cannot be selected", "value": {"type": "(currentDate: dayjs) => boolean", "kind": "expression"}}, {"name": "format", "default": "`YYYY-MM-DD`", "description": "To set the date format, refer to [dayjs](https://day.js.org/). When an array is provided, all values are used for parsing and first value is used for formatting, support [Custom Format](#components-date-picker-demo-format)", "value": {"type": "[formatType](#formattype)", "kind": "expression"}}, {"name": "popupClassName", "default": "-", "description": "To customize the className of the popup calendar", "value": {"type": "string", "kind": "expression"}}, {"name": "getPopupContainer", "default": "-", "description": "To set the container of the floating layer, while the default is to create a `div` element in `body`", "value": {"type": "function(trigger)", "kind": "expression"}}, {"name": "inputReadOnly", "default": "false", "description": "Set the `readonly` attribute of the input tag (avoids virtual keyboard on touch devices)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "locale", "default": "[default](https://github.com/vueComponent/ant-design-vue/blob/main/components/date-picker/locale/example.json)", "description": "Localization configuration", "value": {"type": "object", "kind": "expression"}}, {"name": "mode", "default": "-", "description": "The picker panel mode", "value": {"type": "`time` | `date` | `month` | `year` | `decade`", "kind": "expression"}}, {"name": "nextIcon", "default": "-", "description": "The custom next icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "open", "default": "-", "description": "The open state of picker", "value": {"type": "boolean", "kind": "expression"}}, {"name": "picker", "default": "`date`", "description": "Set picker type", "value": {"type": "`date` | `week` | `month` | `quarter` | `year`", "kind": "expression"}}, {"name": "placeholder", "default": "-", "description": "The placeholder of date input", "value": {"type": "string | [string,string]", "kind": "expression"}}, {"name": "placement", "default": "bottomLeft", "description": "The position where the selection box pops up", "value": {"type": "`bottomLeft` `bottomRight` `topLeft` `topRight`", "kind": "expression"}}, {"name": "popupStyle", "default": "{}", "description": "To customize the style of the popup calendar", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "presets", "default": "-", "description": "The preset ranges for quick selection", "value": {"type": "{ label: slot, value: [dayjs](https://day.js.org/) }[]", "kind": "expression"}}, {"name": "prevIcon", "default": "-", "description": "The custom prev icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "size", "default": "-", "description": "To determine the size of the input box, the height of `large` and `small`, are 40px and 24px respectively, while default size is 32px", "value": {"type": "`large` | `middle` | `small`", "kind": "expression"}}, {"name": "status", "default": "-", "description": "Set validation status", "value": {"type": "'error' | 'warning'", "kind": "expression"}}, {"name": "suffixIcon", "default": "-", "description": "The custom suffix icon", "value": {"type": "v-slot:suffixIcon", "kind": "expression"}}, {"name": "superNextIcon", "default": "-", "description": "The custom super next icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "superPrevIcon", "default": "-", "description": "The custom super prev icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "valueFormat", "default": "-", "description": "optional, format of binding value. If not specified, the binding value will be a Date object", "value": {"type": "string, [date formats](https://day.js.org/docs/en/display/format)", "kind": "expression"}}]}, {"name": "a-descriptions", "slots": [{"name": "extra", "description": "The action area of the description list, placed at the top-right"}, {"name": "title", "description": "The title of the description list, placed at the top"}], "events": [], "attributes": [{"name": "bordered", "default": "false", "description": "whether to display the border", "value": {"type": "boolean", "kind": "expression"}}, {"name": "colon", "default": "true", "description": "change default props `colon` value of `Descriptions.Item`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "column", "default": "3", "description": "the number of `DescriptionItems` in a row,could be a number or a object like `{ xs: 8, sm: 16, md: 24}`,(Only set `bordered={true}` to take effect)", "value": {"type": "number", "kind": "expression"}}, {"name": "contentStyle", "default": "-", "description": "Customize content style", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "extra", "default": "-", "description": "The action area of the description list, placed at the top-right", "value": {"type": "string | VNode | slot", "kind": "expression"}}, {"name": "labelStyle", "default": "-", "description": "Customize label style", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "layout", "default": "`horizontal`", "description": "Define description layout", "value": {"type": "`horizontal` | `vertical`", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "set the size of the list. Can be set to `middle`,`small`, or not filled", "value": {"type": "`default` | `middle` | `small`", "kind": "expression"}}, {"name": "title", "default": "-", "description": "The title of the description list, placed at the top", "value": {"type": "string | VNode | slot", "kind": "expression"}}]}, {"name": "a-divider", "slots": [], "events": [], "attributes": [{"name": "dashed", "default": "false", "description": "whether line is dashed", "value": {"type": "boolean", "kind": "expression"}}, {"name": "orientation", "default": "`center`", "description": "position of title inside divider", "value": {"type": "`left` | `right` | `center`", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "default": "-", "description": "The margin-left/right between the title and its closest border, while the `orientation` must be `left` or `right`", "value": {"type": "string | number", "kind": "expression"}}, {"name": "plain", "default": "true", "description": "Divider text show as plain style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "type", "default": "`horizontal`", "description": "direction type of divider", "value": {"type": "`horizontal` | `vertical`", "kind": "expression"}}]}, {"name": "a-drawer", "slots": [{"name": "closeIcon", "description": "Custom close icon"}, {"name": "extra", "description": "Extra actions area at corner"}, {"name": "footer", "description": "The footer for Drawer"}, {"name": "title", "description": "The title for Drawer"}], "events": [], "attributes": [{"name": "autofocus", "default": "true", "description": "Whether Drawer should get focused after open", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bodyStyle", "default": "-", "description": "Style of the drawer content part", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "class", "default": "-", "description": "Config Drawer Panel className. Use `rootClassName` if want to config top dom style", "value": {"type": "string", "kind": "expression"}}, {"name": "closable", "default": "true", "description": "Whether a close (x) button is visible on top left of the Drawer dialog or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "closeIcon", "default": "`<CloseOutlined />`", "description": "Custom close icon", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "contentWrapperStyle", "default": "-", "description": "Style of the drawer wrapper of content part", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "destroyOnClose", "default": "false", "description": "Whether to unmount child components on closing drawer or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra", "default": "-", "description": "Extra actions area at corner", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "footer", "default": "-", "description": "The footer for Drawer", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "footerStyle", "default": "-", "description": "Style of the drawer footer part", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "forceRender", "default": "false", "description": "Prerender Drawer component forcely", "value": {"type": "boolean", "kind": "expression"}}, {"name": "getContainer", "default": "'body'", "description": "mounted node and display window for Drawer", "value": {"type": "HTMLElement | `() => HTMLElement` | Selectors", "kind": "expression"}}, {"name": "headerStyle", "default": "-", "description": "Style of the drawer header part", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "height", "default": "378", "description": "Placement is `top` or `bottom`, height of the Drawer dialog", "value": {"type": "string | number", "kind": "expression"}}, {"name": "keyboard", "default": "true", "description": "Whether support press esc to close", "value": {"type": "boolean", "kind": "expression"}}, {"name": "mask", "default": "true", "description": "Whether to show mask or not", "value": {"type": "Boolean", "kind": "expression"}}, {"name": "maskClosable", "default": "true", "description": "Clicking on the mask (area outside the Drawer) to close the Drawer or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "maskStyle", "default": "{}", "description": "Style for Drawer's mask element", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "placement", "default": "'right'", "description": "The placement of the Drawer", "value": {"type": "'top' | 'right' | 'bottom' | 'left'", "kind": "expression"}}, {"name": "push", "default": "{ distance: 180 }", "description": "Nested drawers push behavior", "value": {"type": "boolean | {distance: string | number}", "kind": "expression"}}, {"name": "rootClassName", "default": "-", "description": "The class name of the container of the Drawer dialog", "value": {"type": "string", "kind": "expression"}}, {"name": "rootStyle", "default": "-", "description": "Style of wrapper element which **contains mask** compare to `style`", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "style", "default": "-", "description": "Style of Drawer panel. Use `bodyStyle` if want to config body only", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "presetted size of drawer, default `378px` and large `736px`", "value": {"type": "`default` | `large`", "kind": "expression"}}, {"name": "style", "default": "-", "description": "Style of wrapper element which contains mask compare to drawerStyle", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "title", "default": "-", "description": "The title for Drawer", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "open(v-model)", "default": "-", "description": "Whether the Drawer dialog is visible or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "width", "default": "378", "description": "Width of the Drawer dialog", "value": {"type": "string | number", "kind": "expression"}}, {"name": "zIndex", "default": "1000", "description": "The `z-index` of the Drawer", "value": {"type": "Number", "kind": "expression"}}]}, {"name": "a-dropdown", "slots": [], "events": [{"name": "openChange", "description": "a callback function takes an argument: `open`, is executed when the open state is changed. Not trigger when hidden by click item"}], "attributes": [{"name": "align", "default": "-", "description": "this value will be merged into placement's config, please refer to the settings [dom-align](https://github.com/yiminghe/dom-align)", "value": {"type": "Object", "kind": "expression"}}, {"name": "arrow", "default": "false", "description": "Whether the dropdown arrow should be open", "value": {"type": "boolean | { pointAtCenter: boolean }", "kind": "expression"}}, {"name": "destroyPopupOnHide", "default": "false", "description": "Whether destroy dropdown when hidden", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "-", "description": "whether the dropdown menu is disabled", "value": {"type": "boolean", "kind": "expression"}}, {"name": "getPopupContainer", "default": "`() => document.body`", "description": "to set the container of the dropdown menu. The default is to create a `div` element in `body`, you can reset it to the scrolling area and make a relative reposition. [example](https://codepen.io/afc163/pen/zEjNOy?editors=0010)", "value": {"type": "Function(triggerNode)", "kind": "expression"}}, {"name": "overlay(v-slot)", "default": "-", "description": "the dropdown menu", "value": {"type": "[Menu](/components/menu)", "kind": "expression"}}, {"name": "overlayClassName", "default": "-", "description": "Class name of the dropdown root element", "value": {"type": "string", "kind": "expression"}}, {"name": "overlayStyle", "default": "-", "description": "Style of the dropdown root element", "value": {"type": "object", "kind": "expression"}}, {"name": "placement", "default": "`bottomLeft`", "description": "placement of pop menu: `bottomLeft` `bottom` `bottomRight` `topLeft` `top` `topRight`", "value": {"type": "String", "kind": "expression"}}, {"name": "trigger", "default": "`['hover']`", "description": "the trigger mode which executes the drop-down action, hover doesn't work on mobile device", "value": {"type": "Array&lt;`click`|`hover`|`contextmenu`>", "kind": "expression"}}, {"name": "open(v-model)", "default": "-", "description": "whether the dropdown menu is open", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-dropdown-button", "slots": [{"name": "icon", "description": "Icon (appears on the right)"}], "events": [{"name": "click", "description": "a callback function, the same as [Button](/components/button), which will be executed when you click the button on the left"}, {"name": "openChange", "description": "a callback function takes an argument: `open`, is executed when the open state is changed. Not trigger when hidden by click item"}], "attributes": [{"name": "disabled", "default": "-", "description": "whether the dropdown menu is disabled", "value": {"type": "boolean", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "Icon (appears on the right)", "value": {"type": "vNode | slot", "kind": "expression"}}, {"name": "loading", "default": "false", "description": "Set the loading status of button", "value": {"type": "boolean | { delay: number }", "kind": "expression"}}, {"name": "overlay(v-slot)", "default": "-", "description": "the dropdown menu", "value": {"type": "[Menu](/components/menu)", "kind": "expression"}}, {"name": "placement", "default": "`bottomLeft`", "description": "placement of pop menu: `bottomLeft` `bottom` `bottomRight` `topLeft` `top` `topRight`", "value": {"type": "String", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "size of the button, the same as [<PERSON><PERSON>](/components/button)", "value": {"type": "string", "kind": "expression"}}, {"name": "trigger", "default": "`['hover']`", "description": "the trigger mode which executes the drop-down action", "value": {"type": "Array&lt;`click`|`hover`|`contextmenu`>", "kind": "expression"}}, {"name": "type", "default": "`default`", "description": "type of the button, the same as [<PERSON><PERSON>](/components/button)", "value": {"type": "string", "kind": "expression"}}, {"name": "open(v-model)", "default": "-", "description": "whether the dropdown menu is open", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-empty", "slots": [{"name": "description", "description": "Customize description"}, {"name": "image", "description": "Customize image. Will tread as image url when string provided"}], "events": [], "attributes": [{"name": "description", "default": "-", "description": "Customize description", "value": {"type": "string | v-slot", "kind": "expression"}}, {"name": "image", "default": "false", "description": "Customize image. Will tread as image url when string provided", "value": {"type": "string | v-slot", "kind": "expression"}}, {"name": "imageStyle", "default": "-", "description": "style of image", "value": {"type": "CSSProperties", "kind": "expression"}}]}, {"name": "a-flex", "slots": [], "events": [], "attributes": [{"name": "vertical", "default": "`false`", "description": "Is direction of the flex vertical, use `flex-direction: column`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "wrap", "default": "nowrap", "description": "Set whether the element is displayed in a single line or in multiple lines", "value": {"type": "reference [flex-wrap](https://developer.mozilla.org/en-US/docs/Web/CSS/flex-wrap)", "kind": "expression"}}, {"name": "justify", "default": "normal", "description": "Sets the alignment of elements in the direction of the main axis", "value": {"type": "reference [justify-content](https://developer.mozilla.org/en-US/docs/Web/CSS/justify-content)", "kind": "expression"}}, {"name": "align", "default": "normal", "description": "Sets the alignment of elements in the direction of the cross axis", "value": {"type": "reference [align-items](https://developer.mozilla.org/en-US/docs/Web/CSS/align-items)", "kind": "expression"}}, {"name": "flex", "default": "normal", "description": "flex CSS shorthand properties", "value": {"type": "reference [flex](https://developer.mozilla.org/en-US/docs/Web/CSS/flex)", "kind": "expression"}}, {"name": "gap", "default": "-", "description": "Sets the gap between grids", "value": {"type": "`small` | `middle` | `large` | string | number", "kind": "expression"}}, {"name": "component", "default": "`div`", "description": "custom element type", "value": {"type": "Component", "kind": "expression"}}]}, {"name": "a-float-button", "slots": [{"name": "icon", "description": "Set the icon component of button"}, {"name": "description", "description": "Text and other"}, {"name": "tooltip", "description": "The text shown in the tooltip"}], "events": [{"name": "click", "description": "Set the handler to handle `click` event"}], "attributes": [{"name": "icon", "default": "-", "description": "Set the icon component of button", "value": {"type": "slot", "kind": "expression"}}, {"name": "description", "default": "-", "description": "Text and other", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "tooltip", "default": "", "description": "The text shown in the tooltip", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "type", "default": "`default`", "description": "Setting button type", "value": {"type": "`default` | `primary`", "kind": "expression"}}, {"name": "shape", "default": "`circle`", "description": "Setting button shape", "value": {"type": "`circle` | `square`", "kind": "expression"}}, {"name": "href", "default": "-", "description": "The target of hyperlink", "value": {"type": "string", "kind": "expression"}}, {"name": "target", "default": "-", "description": "Specifies where to display the linked URL", "value": {"type": "string", "kind": "expression"}}, {"name": "badge", "default": "-", "description": "Attach Badge to FloatButton. `status` and other props related are not supported.", "value": {"type": "[BadgeProps](/components/badge#api)", "kind": "expression"}}]}, {"name": "a-form", "slots": [], "events": [], "attributes": [{"name": "colon", "default": "true", "description": "change default props colon value of Form.Item (only effective when prop layout is horizontal)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Set form component disable, only available for antdv components", "value": {"type": "boolean", "kind": "expression"}}, {"name": "hideRequiredMark", "default": "false", "description": "Hide required mark of all form items", "value": {"type": "Boolean", "kind": "expression"}}, {"name": "labelAlign", "default": "'right'", "description": "text align of label of all items", "value": {"type": "'left' | 'right'", "kind": "expression"}}, {"name": "labelCol", "default": "", "description": "The layout of label. You can set `span` `offset` to something like `{span: 3, offset: 12}` or `sm: {span: 3, offset: 12}` same as with `<Col>`", "value": {"type": "[object](/components/grid/#col)", "kind": "expression"}}, {"name": "labelWrap", "default": "false", "description": "whether label can be wrap", "value": {"type": "boolean", "kind": "expression"}}, {"name": "layout", "default": "'horizontal'", "description": "Define form layout", "value": {"type": "'horizontal'|'vertical'|'inline'", "kind": "expression"}}, {"name": "model", "default": "", "description": "data of form component", "value": {"type": "object", "kind": "expression"}}, {"name": "name", "default": "-", "description": "Form name. Will be the prefix of Field `id`", "value": {"type": "string", "kind": "expression"}}, {"name": "noStyle", "default": "false", "description": "No style for `true`, used as a pure field control", "value": {"type": "boolean", "kind": "expression"}}, {"name": "rules", "default": "", "description": "validation rules of form", "value": {"type": "object", "kind": "expression"}}, {"name": "scrollToFirstError", "default": "false", "description": "Auto scroll to first failed field when submit", "value": {"type": "boolean | [options](https://github.com/stipsan/scroll-into-view-if-needed/#options)", "kind": "expression"}}, {"name": "validateOnRuleChange", "default": "true", "description": "whether to trigger validation when the `rules` prop is changed", "value": {"type": "boolean", "kind": "expression"}}, {"name": "validate<PERSON><PERSON>ger", "default": "`change`", "description": "Config field validate trigger", "value": {"type": "string | string[]", "kind": "expression"}}, {"name": "wrapperCol", "default": "", "description": "The layout for input controls, same as `labelCol`", "value": {"type": "[object](/components/grid/#col)", "kind": "expression"}}]}, {"name": "a-form-item", "slots": [{"name": "extra", "description": "The extra prompt message. It is similar to help. Usage example: to display error message and prompt message at the same time."}, {"name": "help", "description": "The prompt message. If not provided, the prompt message will be generated by the validation rule."}, {"name": "label", "description": "Label text"}, {"name": "tooltip", "description": "Config tooltip info"}], "events": [], "attributes": [{"name": "autoLink", "default": "true", "description": "Whether to automatically associate form fields. In most cases, you can use automatic association. If the conditions for automatic association are not met, you can manually associate them. See the notes below.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "colon", "default": "true", "description": "Used with `label`, whether to display `:` after label text.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra", "default": "", "description": "The extra prompt message. It is similar to help. Usage example: to display error message and prompt message at the same time.", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "hasFeedback", "default": "false", "description": "Used with `validateStatus`, this option specifies the validation status icon. Recommended to be used only with `Input`.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "help", "default": "", "description": "The prompt message. If not provided, the prompt message will be generated by the validation rule.", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "htmlFor", "default": "", "description": "Set sub label `htmlFor`.", "value": {"type": "string", "kind": "expression"}}, {"name": "label", "default": "", "description": "Label text", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "labelAlign", "default": "'right'", "description": "text align of label", "value": {"type": "'left' | 'right'", "kind": "expression"}}, {"name": "labelCol", "default": "", "description": "The layout of label. You can set `span` `offset` to something like `{span: 3, offset: 12}` or `sm: {span: 3, offset: 12}` same as with `<Col>`", "value": {"type": "[object](/components/grid/#col)", "kind": "expression"}}, {"name": "name", "default": "", "description": "a key of `model`. In the use of validate and resetFields method, the attribute is required", "value": {"type": "[NamePath](#namepath)", "kind": "expression"}}, {"name": "required", "default": "false", "description": "Whether provided or not, it will be generated by the validation rule.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "rules", "default": "", "description": "validation rules of form", "value": {"type": "object | array", "kind": "expression"}}, {"name": "tooltip", "default": "", "description": "Config tooltip info", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "validate<PERSON><PERSON><PERSON>", "default": "false", "description": "Whether stop validate on first rule of error for this field.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "validateStatus", "default": "", "description": "The validation status. If not provided, it will be generated by validation rule. options: 'success' 'warning' 'error' 'validating'", "value": {"type": "string", "kind": "expression"}}, {"name": "validate<PERSON><PERSON>ger", "default": "`change`", "description": "When to validate the value of children node", "value": {"type": "string | string[]", "kind": "expression"}}, {"name": "wrapperCol", "default": "", "description": "The layout for input controls, same as `labelCol`", "value": {"type": "[object](/components/grid/#col)", "kind": "expression"}}]}, {"name": "a-grid", "slots": [], "events": [], "attributes": []}, {"name": "a-icon", "slots": [], "events": [], "attributes": []}, {"name": "a-common", "slots": [], "events": [], "attributes": [{"name": "rotate", "default": "-", "description": "Rotate by n degrees (not working in IE9)", "value": {"type": "number", "kind": "expression"}}, {"name": "spin", "default": "false", "description": "Rotate icon with animation", "value": {"type": "boolean", "kind": "expression"}}, {"name": "style", "default": "-", "description": "Style properties of icon, like `fontSize` and `color`", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "twoToneColor", "default": "-", "description": "Only supports the two-tone icon. Specify the primary color.", "value": {"type": "string (hex color)", "kind": "expression"}}]}, {"name": "a-custom", "slots": [], "events": [], "attributes": [{"name": "component", "default": "-", "description": "The component used for the root node.", "value": {"type": "ComponentType&lt;CustomIconComponentProps>", "kind": "expression"}}, {"name": "rotate", "default": "-", "description": "Rotate degrees (not working in IE9)", "value": {"type": "number", "kind": "expression"}}, {"name": "spin", "default": "false", "description": "Rotate icon with animation", "value": {"type": "boolean", "kind": "expression"}}, {"name": "style", "default": "-", "description": "Style properties of icon, like `fontSize` and `color`", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "extraCommonProps", "default": "{}", "description": "Define extra properties to the component", "value": {"type": "`{ class, attrs, props, on, style }`", "kind": "expression"}}, {"name": "scriptUrl", "default": "-", "description": "The URL generated by [iconfont.cn](http://iconfont.cn/) project.", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-image", "slots": [{"name": "placeholder", "description": "Load placeholder, use default placeholder when set `true`"}, {"name": "previewMask", "description": "custom mask"}], "events": [{"name": "error", "description": "<PERSON><PERSON> failed callback"}], "attributes": [{"name": "alt", "default": "-", "description": "Image description", "value": {"type": "string", "kind": "expression"}}, {"name": "fallback", "default": "-", "description": "Load failure fault-tolerant src", "value": {"type": "string", "kind": "expression"}}, {"name": "height", "default": "-", "description": "Image height", "value": {"type": "string | number", "kind": "expression"}}, {"name": "placeholder", "default": "-", "description": "Load placeholder, use default placeholder when set `true`", "value": {"type": "boolean | slot", "kind": "expression"}}, {"name": "preview", "default": "true", "description": "preview config, disabled when `false`", "value": {"type": "boolean | [previewType](#previewtype)", "kind": "expression"}}, {"name": "src", "default": "-", "description": "Image path", "value": {"type": "string", "kind": "expression"}}, {"name": "previewMask", "default": "-", "description": "custom mask", "value": {"type": "false | function | slot", "kind": "expression"}}, {"name": "width", "default": "-", "description": "Image width", "value": {"type": "string | number", "kind": "expression"}}]}, {"name": "a-input", "slots": [{"name": "addonAfter", "description": "The label text displayed after (on the right side of) the input field."}, {"name": "addonBefore", "description": "The label text displayed before (on the left side of) the input field."}, {"name": "clearIcon", "description": "custom clear icon when allowClear"}, {"name": "prefix", "description": "The prefix icon for the Input."}, {"name": "suffix", "description": "The suffix icon for the Input."}], "events": [], "attributes": [{"name": "addonAfter", "default": "", "description": "The label text displayed after (on the right side of) the input field.", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "addonBefore", "default": "", "description": "The label text displayed before (on the left side of) the input field.", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "allowClear", "default": "", "description": "allow to remove input content with clear icon", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bordered", "default": "true", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clearIcon", "default": "`<CloseCircleFilled />`", "description": "custom clear icon when allowClear", "value": {"type": "slot", "kind": "expression"}}, {"name": "defaultValue", "default": "", "description": "The initial input content", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Whether the input is disabled.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "id", "default": "", "description": "The ID for input", "value": {"type": "string", "kind": "expression"}}, {"name": "maxlength", "default": "", "description": "max length", "value": {"type": "number", "kind": "expression"}}, {"name": "prefix", "default": "", "description": "The prefix icon for the Input.", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "showCount", "default": "false", "description": "Whether show text count", "value": {"type": "boolean", "kind": "expression"}}, {"name": "status", "default": "-", "description": "Set validation status", "value": {"type": "'error' | 'warning'", "kind": "expression"}}, {"name": "size", "default": "-", "description": "The size of the input box. Note: in the context of a form, the `middle` size is used. Available: `large` `middle` `small`", "value": {"type": "string", "kind": "expression"}}, {"name": "suffix", "default": "", "description": "The suffix icon for the Input.", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "type", "default": "`text`", "description": "The type of input, see: [MDN](https://developer.mozilla.org/docs/Web/HTML/Element/input#Form_%3Cinput%3E_types)(use `<a-textarea />` instead of `type=\"textarea\"`)", "value": {"type": "string", "kind": "expression"}}, {"name": "value(v-model)", "default": "", "description": "The input content value", "value": {"type": "string", "kind": "expression"}}, {"name": "change", "default": "", "description": "callback when user input", "value": {"type": "function(e)", "kind": "expression"}}, {"name": "pressEnter", "default": "", "description": "The callback function that is triggered when Enter key is pressed.", "value": {"type": "function(e)", "kind": "expression"}}]}, {"name": "a-input-search", "slots": [{"name": "enterButton", "description": "to show an enter button after input. This prop is conflict with addon."}], "events": [], "attributes": [{"name": "enterButton", "default": "false", "description": "to show an enter button after input. This prop is conflict with addon.", "value": {"type": "boolean|slot", "kind": "expression"}}, {"name": "loading", "default": "", "description": "Search box with loading.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "search", "default": "", "description": "The callback function that is triggered when you click on the search-icon or press Enter key.", "value": {"type": "function(value, event)", "kind": "expression"}}]}, {"name": "a-input-group", "slots": [], "events": [], "attributes": [{"name": "compact", "default": "false", "description": "Whether use compact style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "The size of `Input.Group` specifies the size of the included `Input` fields. Available: `large` `default` `small`", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-input-password", "slots": [{"name": "iconRender", "description": "Custom toggle button"}], "events": [], "attributes": [{"name": "visible(v-model)", "default": "false", "description": "password visibility", "value": {"type": "boolean", "kind": "expression"}}, {"name": "iconRender", "default": "-", "description": "Custom toggle button", "value": {"type": "slot", "kind": "expression"}}, {"name": "visibilityToggle", "default": "true", "description": "Whether show toggle button or control password visible", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-input-number", "slots": [{"name": "addonAfter", "description": "The label text displayed after (on the right side of) the input field"}, {"name": "addonBefore", "description": "The label text displayed before (on the left side of) the input field"}, {"name": "prefix", "description": "The prefix icon for the Input"}, {"name": "upIcon", "description": "custom up icon"}, {"name": "downIcon", "description": "custom up down"}], "events": [{"name": "change", "description": "The callback triggered when the value is changed."}, {"name": "pressEnter", "description": "The callback function that is triggered when Enter key is pressed."}, {"name": "step", "description": "The callback function that is triggered when click up or down buttons"}], "attributes": [{"name": "addonAfter", "default": "-", "description": "The label text displayed after (on the right side of) the input field", "value": {"type": "slot", "kind": "expression"}}, {"name": "addonBefore", "default": "-", "description": "The label text displayed before (on the left side of) the input field", "value": {"type": "slot", "kind": "expression"}}, {"name": "autofocus", "default": "false", "description": "get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bordered", "default": "true", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "controls", "default": "true", "description": "Whether to show `+-` controls", "value": {"type": "boolean", "kind": "expression"}}, {"name": "decimalSeparator", "default": "-", "description": "decimal separator", "value": {"type": "string", "kind": "expression"}}, {"name": "defaultValue", "default": "", "description": "initial value", "value": {"type": "number", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "disable the input", "value": {"type": "boolean", "kind": "expression"}}, {"name": "formatter", "default": "-", "description": "Specifies the format of the value presented", "value": {"type": "function(value: number | string, info: { userTyping: boolean, input: string }): string", "kind": "expression"}}, {"name": "keyboard", "default": "true", "description": "If enable keyboard behavior", "value": {"type": "boolean", "kind": "expression"}}, {"name": "max", "default": "Infinity", "description": "max value", "value": {"type": "number", "kind": "expression"}}, {"name": "min", "default": "-Infinity", "description": "min value", "value": {"type": "number", "kind": "expression"}}, {"name": "parser", "default": "-", "description": "Specifies the value extracted from formatter", "value": {"type": "function( string): number", "kind": "expression"}}, {"name": "precision", "default": "-", "description": "precision of input value", "value": {"type": "number", "kind": "expression"}}, {"name": "prefix", "default": "-", "description": "The prefix icon for the Input", "value": {"type": "slot", "kind": "expression"}}, {"name": "size", "default": "-", "description": "height of input box", "value": {"type": "string", "kind": "expression"}}, {"name": "status", "default": "-", "description": "Set validation status", "value": {"type": "'error' | 'warning'", "kind": "expression"}}, {"name": "step", "default": "1", "description": "The number to which the current value is increased or decreased. It can be an integer or decimal.", "value": {"type": "number|string", "kind": "expression"}}, {"name": "stringMode", "default": "false", "description": "Set value as string to support high precision decimals. Will return string value by `change`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "value(v-model)", "default": "", "description": "current value", "value": {"type": "number", "kind": "expression"}}, {"name": "upIcon", "default": "`<UpOutlined />`", "description": "custom up icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "downIcon", "default": "`<DownOutlined />`", "description": "custom up down", "value": {"type": "slot", "kind": "expression"}}]}, {"name": "a-layout", "slots": [], "events": [], "attributes": [{"name": "class", "default": "-", "description": "container className", "value": {"type": "string", "kind": "expression"}}, {"name": "hasSider", "default": "-", "description": "whether contain Sider in children, don't have to assign it normally. Useful in ssr avoid style flickering", "value": {"type": "boolean", "kind": "expression"}}, {"name": "style", "default": "-", "description": "to customize the styles", "value": {"type": "object|string", "kind": "expression"}}]}, {"name": "a-layout-sider", "slots": [{"name": "trigger", "description": "specify the customized trigger, set to null to hide the trigger"}], "events": [], "attributes": [{"name": "breakpoint", "default": "-", "description": "[breakpoints](/components/grid#api) of the responsive layout", "value": {"type": "`xs` | `sm` | `md` | `lg` | `xl` | `xxl`", "kind": "expression"}}, {"name": "class", "default": "-", "description": "container className", "value": {"type": "string", "kind": "expression"}}, {"name": "collapsed(v-model)", "default": "-", "description": "to set the current status", "value": {"type": "boolean", "kind": "expression"}}, {"name": "collapsedWidth", "default": "80", "description": "width of the collapsed sidebar, by setting to `0` a special trigger will appear", "value": {"type": "number", "kind": "expression"}}, {"name": "collapsible", "default": "false", "description": "whether can be collapsed", "value": {"type": "boolean", "kind": "expression"}}, {"name": "defaultCollapsed", "default": "false", "description": "to set the initial status", "value": {"type": "boolean", "kind": "expression"}}, {"name": "reverseArrow", "default": "false", "description": "reverse direction of arrow, for a sider that expands from the right", "value": {"type": "boolean", "kind": "expression"}}, {"name": "style", "default": "-", "description": "to customize the styles", "value": {"type": "object|string", "kind": "expression"}}, {"name": "theme", "default": "`dark`", "description": "color theme of the sidebar", "value": {"type": "`light` | `dark`", "kind": "expression"}}, {"name": "trigger", "default": "-", "description": "specify the customized trigger, set to null to hide the trigger", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "width", "default": "200", "description": "width of the sidebar", "value": {"type": "number|string", "kind": "expression"}}, {"name": "zeroWidthTriggerStyle", "default": "-", "description": "to customize the styles of the special trigger that appears when `collapsedWidth` is 0", "value": {"type": "object", "kind": "expression"}}]}, {"name": "a-list", "slots": [{"name": "footer", "description": "List footer renderer"}, {"name": "header", "description": "List header renderer"}, {"name": "loadMore", "description": "Shows a load more content"}], "events": [], "attributes": [{"name": "bordered", "default": "false", "description": "Toggles rendering of the border around the list", "value": {"type": "boolean", "kind": "expression"}}, {"name": "dataSource", "default": "-", "description": "dataSource array for list", "value": {"type": "any[]", "kind": "expression"}}, {"name": "footer", "default": "-", "description": "List footer renderer", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "grid", "default": "-", "description": "The grid type of list. You can set grid to something like {gutter: 16, column: 4}", "value": {"type": "object", "kind": "expression"}}, {"name": "header", "default": "-", "description": "List header renderer", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "itemLayout", "default": "-", "description": "The layout of list, default is `horizontal`, If a vertical list is desired, set the itemLayout property to `vertical`", "value": {"type": "string", "kind": "expression"}}, {"name": "loading", "default": "false", "description": "Shows a loading indicator while the contents of the list are being fetched", "value": {"type": "boolean|[object](https://www.antdv.com/components/spin/#api)", "kind": "expression"}}, {"name": "loadMore", "default": "-", "description": "Shows a load more content", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "locale", "default": "emptyText: 'No Data' <br>", "description": "i18n text including empty text", "value": {"type": "object", "kind": "expression"}}, {"name": "pagination", "default": "false", "description": "Pagination [config](https://www.antdv.com/components/pagination/#api), hide it by setting it to false", "value": {"type": "boolean | object", "kind": "expression"}}, {"name": "renderItem", "default": "", "description": "Custom item renderer, #renderItem=\"{item, index}\"", "value": {"type": "({item, index}) => vNode", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "default": "`key`", "description": "<PERSON><PERSON>'s unique key, could be a string or function that returns a string", "value": {"type": "string|Function(record):string", "kind": "expression"}}, {"name": "split", "default": "true", "description": "Toggles rendering of the split under the list item", "value": {"type": "boolean", "kind": "expression"}}, {"name": "column", "default": "-", "description": "column of grid", "value": {"type": "number oneOf [ 1, 2, 3, 4, 6, 8, 12, 24]", "kind": "expression"}}, {"name": "gutter", "default": "0", "description": "spacing between grid", "value": {"type": "number", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "Size of list", "value": {"type": "`default` | `middle` | `small`", "kind": "expression"}}, {"name": "xxxl", "default": "-", "description": "`≥2000px` column of grid", "value": {"type": "number", "kind": "expression"}}, {"name": "xs", "default": "-", "description": "`<576px` column of grid", "value": {"type": "number", "kind": "expression"}}, {"name": "sm", "default": "-", "description": "`≥576px` column of grid", "value": {"type": "number", "kind": "expression"}}, {"name": "md", "default": "-", "description": "`≥768px` column of grid", "value": {"type": "number", "kind": "expression"}}, {"name": "lg", "default": "-", "description": "`≥992px` column of grid", "value": {"type": "number", "kind": "expression"}}, {"name": "xl", "default": "-", "description": "`≥1200px` column of grid", "value": {"type": "number", "kind": "expression"}}, {"name": "xxl", "default": "-", "description": "`≥1600px` column of grid", "value": {"type": "number", "kind": "expression"}}]}, {"name": "a-list-item", "slots": [{"name": "actions", "description": "The actions content of list item. If `itemLayout` is `vertical`, shows the content on bottom, otherwise shows content on the far right."}, {"name": "extra", "description": "The extra content of list item. If `itemLayout` is `vertical`, shows the content on right, otherwise shows content on the far right."}], "events": [], "attributes": [{"name": "actions", "default": "-", "description": "The actions content of list item. If `itemLayout` is `vertical`, shows the content on bottom, otherwise shows content on the far right.", "value": {"type": "vNode[] |slot", "kind": "expression"}}, {"name": "extra", "default": "-", "description": "The extra content of list item. If `itemLayout` is `vertical`, shows the content on right, otherwise shows content on the far right.", "value": {"type": "string|slot", "kind": "expression"}}]}, {"name": "a-list-item-meta", "slots": [{"name": "avatar", "description": "The avatar of list item"}, {"name": "description", "description": "The description of list item"}, {"name": "title", "description": "The title of list item"}], "events": [], "attributes": [{"name": "avatar", "default": "-", "description": "The avatar of list item", "value": {"type": "slot", "kind": "expression"}}, {"name": "description", "default": "-", "description": "The description of list item", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "title", "default": "-", "description": "The title of list item", "value": {"type": "string|slot", "kind": "expression"}}]}, {"name": "a-mentions", "slots": [], "events": [], "attributes": []}, {"name": "a-menu", "slots": [{"name": "overflowedIndicator", "description": "Customized the ellipsis icon when menu is collapsed horizontally"}], "events": [], "attributes": [{"name": "forceSubMenuRender", "default": "false", "description": "render submenu into DOM before it shows", "value": {"type": "boolean", "kind": "expression"}}, {"name": "inlineCollapsed", "default": "-", "description": "specifies the collapsed status when menu is inline mode", "value": {"type": "boolean", "kind": "expression"}}, {"name": "inlineIndent", "default": "24", "description": "indent px of inline menu item on each level", "value": {"type": "number", "kind": "expression"}}, {"name": "items", "default": "-", "description": "Menu item content", "value": {"type": "[ItemType[]](#itemtype)", "kind": "expression"}}, {"name": "mode", "default": "`vertical`", "description": "type of the menu; `vertical`, `horizontal`, and `inline` modes are supported", "value": {"type": "`vertical` | `horizontal` | `inline`", "kind": "expression"}}, {"name": "multiple", "default": "false", "description": "Allow selection of multiple items", "value": {"type": "boolean", "kind": "expression"}}, {"name": "openKeys(v-model)", "default": "", "description": "array with the keys of currently opened sub menus", "value": {"type": "(string | number)[]", "kind": "expression"}}, {"name": "overflowedIndicator", "default": "`<EllipsisOutlined />`", "description": "Customized the ellipsis icon when menu is collapsed horizontally", "value": {"type": "slot", "kind": "expression"}}, {"name": "selectable", "default": "true", "description": "allow selecting menu items", "value": {"type": "boolean", "kind": "expression"}}, {"name": "selected<PERSON>eys(v-model)", "default": "", "description": "array with the keys of currently selected menu items", "value": {"type": "(string | number)[]", "kind": "expression"}}, {"name": "style", "default": "", "description": "style of the root node", "value": {"type": "object", "kind": "expression"}}, {"name": "subMenuCloseDelay", "default": "0.1", "description": "delay time to hide submenu when mouse leave, unit: second", "value": {"type": "number", "kind": "expression"}}, {"name": "subMenuOpenDelay", "default": "0", "description": "delay time to show submenu when mouse enter, unit: second", "value": {"type": "number", "kind": "expression"}}, {"name": "theme", "default": "`light`", "description": "color theme of the menu", "value": {"type": "`light` | `dark`", "kind": "expression"}}, {"name": "triggerSubMenuAction", "default": "`hover`", "description": "method of trigger submenu", "value": {"type": "`click` | `hover`", "kind": "expression"}}, {"name": "click", "description": "callback executed when a menu item is clicked", "value": {"type": "function({ item, key, keyPath })", "kind": "expression"}}, {"name": "deselect", "description": "callback executed when a menu item is deselected, only supported for multiple mode", "value": {"type": "function({ item, key, selectedKeys })", "kind": "expression"}}, {"name": "openChange", "description": "called when open/close sub menu", "value": {"type": "function(openKeys: (string | number)[])", "kind": "expression"}}, {"name": "select", "description": "callback executed when a menu item is selected", "value": {"type": "function({ item, key, selectedKeys })", "kind": "expression"}}]}, {"name": "a-menu-item", "slots": [{"name": "title", "description": "set display title for collapsed item"}], "events": [], "attributes": [{"name": "disabled", "default": "false", "description": "whether menu item is disabled or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "key", "default": "", "description": "unique id of the menu item", "value": {"type": "string | number", "kind": "expression"}}, {"name": "title", "default": "", "description": "set display title for collapsed item", "value": {"type": "string | slot", "kind": "expression"}}]}, {"name": "a-menu-item-type", "slots": [], "events": [], "attributes": [{"name": "danger", "default": "false", "description": "Display the danger style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Whether menu item is disabled", "value": {"type": "boolean", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "The icon of the menu item", "value": {"type": "VueNode | (item: MenuItemType) => VNode", "kind": "expression"}}, {"name": "key", "default": "-", "description": "Unique ID of the menu item", "value": {"type": "string | number", "kind": "expression"}}, {"name": "label", "default": "-", "description": "Menu label", "value": {"type": "VueNode", "kind": "expression"}}, {"name": "title", "default": "-", "description": "Set display title for collapsed item", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-sub-menu-type", "slots": [], "events": [], "attributes": [{"name": "children", "default": "-", "description": "Sub-menus or sub-menu items", "value": {"type": "[ItemType[]](#itemtype)", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Whether sub-menu is disabled", "value": {"type": "boolean", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "Icon of sub menu", "value": {"type": "VueNode | (item: SubMenuType) => VueNode", "kind": "expression"}}, {"name": "key", "default": "-", "description": "Unique ID of the sub-menu", "value": {"type": "string | number", "kind": "expression"}}, {"name": "label", "default": "-", "description": "Menu label", "value": {"type": "VueNode", "kind": "expression"}}, {"name": "popupClassName", "default": "-", "description": "Sub-menu class name, not working when `mode=\"inline\"`", "value": {"type": "string", "kind": "expression"}}, {"name": "popupOffset", "default": "-", "description": "Sub-menu offset, not working when `mode=\"inline\"`", "value": {"type": "[number, number]", "kind": "expression"}}, {"name": "theme", "default": "`light` | `dark`", "description": "Color theme of the SubMenu (inherits from <PERSON><PERSON> by default)", "value": {"type": "", "kind": "expression"}}, {"name": "onTitleClick", "default": "-", "description": "Callback executed when the sub-menu title is clicked", "value": {"type": "function({ key, domEvent })", "kind": "expression"}}]}, {"name": "a-menu-item-group-type", "slots": [], "events": [], "attributes": [{"name": "children", "default": "-", "description": "Sub-menu items", "value": {"type": "[MenuItemType[]](#menuitemtype)", "kind": "expression"}}, {"name": "label", "default": "-", "description": "The title of the group", "value": {"type": "VueNode", "kind": "expression"}}]}, {"name": "a-menu-divider-type", "slots": [], "events": [], "attributes": [{"name": "dashed", "default": "false", "description": "Whether line is dashed", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-menu-sub-menu", "slots": [{"name": "expandIcon", "description": "Customized expandIcon"}, {"name": "title", "description": "title of the sub menu"}], "events": [], "attributes": [{"name": "disabled", "default": "false", "description": "whether sub menu is disabled or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "expandIcon", "default": "arrow icon", "description": "Customized expandIcon", "value": {"type": "slot", "kind": "expression"}}, {"name": "key", "default": "", "description": "Unique ID of the sub menu, required", "value": {"type": "string | number", "kind": "expression"}}, {"name": "popupClassName", "default": "", "description": "Sub-menu class name", "value": {"type": "string", "kind": "expression"}}, {"name": "popupOffset", "default": "-", "description": "Sub-menu offset, not working when `mode=\"inline\"`", "value": {"type": "[number, number]", "kind": "expression"}}, {"name": "title", "default": "", "description": "title of the sub menu", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "titleClick", "description": "callback executed when the sub menu title is clicked", "value": {"type": "function({ key, domEvent })", "kind": "expression"}}]}, {"name": "a-menu-item-group", "slots": [{"name": "title", "description": "title of the group"}], "events": [], "attributes": [{"name": "children", "default": "", "description": "sub-menu items", "value": {"type": "MenuItem[]", "kind": "expression"}}, {"name": "title", "default": "", "description": "title of the group", "value": {"type": "string|slot", "kind": "expression"}}]}, {"name": "a-menu-divider", "slots": [], "events": [], "attributes": [{"name": "dashed", "default": "false", "description": "Whether line is dashed", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-message", "slots": [], "events": [], "attributes": [{"name": "content", "default": "-", "description": "content of the message", "value": {"type": "string| VNode | () => VNode", "kind": "expression"}}, {"name": "duration", "default": "1.5", "description": "time(seconds) before auto-dismiss, don't dismiss if set to 0", "value": {"type": "number", "kind": "expression"}}, {"name": "onClose", "default": "-", "description": "Specify a function that will be called when the message is closed", "value": {"type": "function", "kind": "expression"}}]}, {"name": "a-modal", "slots": [{"name": "cancelText", "description": "Text of the Cancel button"}, {"name": "closeIcon", "description": "custom close icon"}, {"name": "footer", "description": "Footer content, set as `:footer=\"null\"` when you don't need default buttons"}, {"name": "okText", "description": "Text of the OK button"}, {"name": "title", "description": "The modal dialog's title"}], "events": [{"name": "cancel", "description": "Specify a function that will be called when a user clicks mask, close button on top right or Cancel button"}, {"name": "ok", "description": "Specify a function that will be called when a user clicks the OK button"}], "attributes": [{"name": "afterClose", "default": "-", "description": "Specify a function that will be called when modal is closed completely.", "value": {"type": "function", "kind": "expression"}}, {"name": "bodyStyle", "default": "{}", "description": "Body style for modal body element. Such as height, padding etc.", "value": {"type": "object", "kind": "expression"}}, {"name": "cancelButtonProps", "default": "-", "description": "The cancel button props", "value": {"type": "[ButtonProps](/components/button/#api)", "kind": "expression"}}, {"name": "cancelText", "default": "`Cancel`", "description": "Text of the Cancel button", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "centered", "default": "`false`", "description": "Centered Modal", "value": {"type": "boolean", "kind": "expression"}}, {"name": "closable", "default": "true", "description": "Whether a close (x) button is visible on top right of the modal dialog or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "closeIcon", "default": "-", "description": "custom close icon", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "confirmLoading", "default": "false", "description": "Whether to apply loading visual effect for OK button or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "destroyOnClose", "default": "false", "description": "Whether to unmount child components on onClose", "value": {"type": "boolean", "kind": "expression"}}, {"name": "footer", "default": "OK and Cancel buttons", "description": "Footer content, set as `:footer=\"null\"` when you don't need default buttons", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "forceRender", "default": "false", "description": "Force render Modal", "value": {"type": "boolean", "kind": "expression"}}, {"name": "getContainer", "default": "() => document.body", "description": "Return the mount node for Modal", "value": {"type": "(instance): HTMLElement", "kind": "expression"}}, {"name": "mask", "default": "true", "description": "Whether show mask or not.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "maskClosable", "default": "true", "description": "Whether to close the modal dialog when the mask (area outside the modal) is clicked", "value": {"type": "boolean", "kind": "expression"}}, {"name": "maskStyle", "default": "{}", "description": "Style for modal's mask element.", "value": {"type": "object", "kind": "expression"}}, {"name": "okButtonProps", "default": "-", "description": "The ok button props", "value": {"type": "[ButtonProps](/components/button/#api)", "kind": "expression"}}, {"name": "okText", "default": "`OK`", "description": "Text of the OK button", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "okType", "default": "`primary`", "description": "Button `type` of the OK button", "value": {"type": "string", "kind": "expression"}}, {"name": "title", "default": "-", "description": "The modal dialog's title", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "open(v-model)", "default": "false", "description": "Whether the modal dialog is visible or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "width", "default": "520", "description": "Width of the modal dialog", "value": {"type": "string|number", "kind": "expression"}}, {"name": "wrapClassName", "default": "-", "description": "The class name of the container of the modal dialog", "value": {"type": "string", "kind": "expression"}}, {"name": "zIndex", "default": "1000", "description": "The `z-index` of the Modal", "value": {"type": "number", "kind": "expression"}}]}, {"name": "a-notification", "slots": [], "events": [], "attributes": [{"name": "bottom", "default": "`24px`", "description": "Distance from the bottom of the viewport, when `placement` is `bottomRight` or `bottomLeft` (unit: pixels).", "value": {"type": "string", "kind": "expression"}}, {"name": "btn", "default": "-", "description": "Customized close button", "value": {"type": "VNode | () => VNode", "kind": "expression"}}, {"name": "class", "default": "-", "description": "Customized CSS class", "value": {"type": "string", "kind": "expression"}}, {"name": "closeIcon", "default": "-", "description": "custom close icon", "value": {"type": "VNode | () => VNode", "kind": "expression"}}, {"name": "description", "default": "-", "description": "The content of notification box (required)", "value": {"type": "string| VNode | () => VNode", "kind": "expression"}}, {"name": "duration", "default": "4.5", "description": "Time in seconds before Notification is closed. When set to 0 or null, it will never be closed automatically", "value": {"type": "number", "kind": "expression"}}, {"name": "getContainer", "default": "() => document.body", "description": "Return the mount node for Notification", "value": {"type": "() => HTMLNode", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "Customized icon", "value": {"type": "VNode | () => VNode", "kind": "expression"}}, {"name": "key", "default": "-", "description": "The unique identifier of the Notification", "value": {"type": "string", "kind": "expression"}}, {"name": "message", "default": "-", "description": "The title of notification box (required)", "value": {"type": "string| VNode | () => VNode", "kind": "expression"}}, {"name": "placement", "default": "`topRight`", "description": "Position of Notification, can be one of `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight`", "value": {"type": "string", "kind": "expression"}}, {"name": "style", "default": "-", "description": "Customized inline style", "value": {"type": "Object | string", "kind": "expression"}}, {"name": "top", "default": "`24px`", "description": "Distance from the top of the viewport, when `placement` is `topRight` or `topLeft` (unit: pixels).", "value": {"type": "string", "kind": "expression"}}, {"name": "onClick", "default": "-", "description": "Specify a function that will be called when the notification is clicked", "value": {"type": "Function", "kind": "expression"}}, {"name": "onClose", "default": "-", "description": "Specify a function that will be called when the close button is clicked", "value": {"type": "Function", "kind": "expression"}}]}, {"name": "a-page-header", "slots": [{"name": "backIcon", "description": "custom back icon, if false the back icon will not be displayed"}, {"name": "extra", "description": "Operating area, at the end of the line of the title line"}, {"name": "footer", "description": "<PERSON><PERSON><PERSON><PERSON>'s footer, generally used to render TabBar"}, {"name": "subTitle", "description": "custom subTitle text"}, {"name": "title", "description": "custom title text"}], "events": [], "attributes": [{"name": "avatar", "default": "-", "description": "<PERSON><PERSON> next to the title bar", "value": {"type": "[avatar props](/components/avatar/)", "kind": "expression"}}, {"name": "backIcon", "default": "`<ArrowLeft />`", "description": "custom back icon, if false the back icon will not be displayed", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "breadcrumb", "default": "-", "description": "Breadcrumb configuration", "value": {"type": "[breadcrumb](/components/breadcrumb/)", "kind": "expression"}}, {"name": "extra", "default": "-", "description": "Operating area, at the end of the line of the title line", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "footer", "default": "-", "description": "<PERSON><PERSON><PERSON><PERSON>'s footer, generally used to render TabBar", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "ghost", "default": "true", "description": "PageHeader type, will change background color", "value": {"type": "boolean", "kind": "expression"}}, {"name": "subTitle", "default": "-", "description": "custom subTitle text", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "tags", "default": "-", "description": "Tag list next to title", "value": {"type": "[Tag](/components/tag/)[] | [Tag](/components/tag/)", "kind": "expression"}}, {"name": "title", "default": "-", "description": "custom title text", "value": {"type": "string|slot", "kind": "expression"}}]}, {"name": "a-pagination", "slots": [{"name": "itemRender", "description": "to customize item innerHTML"}], "events": [{"name": "change", "description": "Called when the page number or `pageSize` is changed, and it takes the resulting page number and pageSize as its arguments"}, {"name": "showSizeChange", "description": "a callback function, executed when `pageSize` is changed"}], "attributes": [{"name": "current(v-model)", "default": "-", "description": "current page number", "value": {"type": "number", "kind": "expression"}}, {"name": "defaultCurrent", "default": "1", "description": "default initial page number", "value": {"type": "number", "kind": "expression"}}, {"name": "defaultPageSize", "default": "10", "description": "default number of data items per page", "value": {"type": "number", "kind": "expression"}}, {"name": "disabled", "default": "-", "description": "Disable pagination", "value": {"type": "boolean", "kind": "expression"}}, {"name": "hideOnSinglePage", "default": "false", "description": "Whether to hide pager on single page", "value": {"type": "boolean", "kind": "expression"}}, {"name": "itemRender", "default": "-", "description": "to customize item innerHTML", "value": {"type": "(page, type: 'page' | 'prev' | 'next', originalElement) => vNode | v-slot", "kind": "expression"}}, {"name": "pageSize(v-model)", "default": "-", "description": "number of data items per page", "value": {"type": "number", "kind": "expression"}}, {"name": "pageSizeOptions", "default": "\\['10', '20', '50', '100']", "description": "specify the sizeChanger options", "value": {"type": "string[] | number[]", "kind": "expression"}}, {"name": "responsive", "default": "-", "description": "If `size` is not specified, `Pagination` would resize according to the width of the window", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showLessItems", "default": "false", "description": "Show less page items", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showQuickJumper", "default": "false", "description": "determine whether you can jump to pages directly", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showSizeChanger", "default": "-", "description": "Determine whether to show `pageSize` select, it will be true when `total > 50`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showTitle", "default": "true", "description": "Show page item's title", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showTotal", "default": "-", "description": "to display the total number and range", "value": {"type": "Function(total, range)", "kind": "expression"}}, {"name": "simple", "default": "-", "description": "whether to use simple mode", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "\"\"", "description": "specify the size of `Pagination`, can be set to `small`", "value": {"type": "string", "kind": "expression"}}, {"name": "total", "default": "0", "description": "total number of data items", "value": {"type": "number", "kind": "expression"}}]}, {"name": "a-popconfirm", "slots": [{"name": "cancelButton", "description": "custom render cancel button"}, {"name": "cancelText", "description": "text of the Cancel button"}, {"name": "icon", "description": "customize icon of confirmation"}, {"name": "okButton", "description": "custom render confirm button"}, {"name": "okText", "description": "text of the Confirm button"}, {"name": "title", "description": "title of the confirmation box"}, {"name": "description", "description": "The description of the confirmation box title"}], "events": [{"name": "cancel", "description": "callback of cancel"}, {"name": "confirm", "description": "callback of confirmation"}, {"name": "openChange", "description": "Callback executed when visibility of the tooltip card is changed"}], "attributes": [{"name": "cancelButton", "default": "-", "description": "custom render cancel button", "value": {"type": "slot", "kind": "expression"}}, {"name": "cancelButtonProps", "default": "-", "description": "The cancel button props", "value": {"type": "[ButtonProps](/components/button/#api)", "kind": "expression"}}, {"name": "cancelText", "default": "`Cancel`", "description": "text of the Cancel button", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "is show popconfirm when click its childrenNode", "value": {"type": "boolean", "kind": "expression"}}, {"name": "icon", "default": "&lt;Icon type=\"exclamation-circle\" />", "description": "customize icon of confirmation", "value": {"type": "vNode|slot", "kind": "expression"}}, {"name": "okButton", "default": "-", "description": "custom render confirm button", "value": {"type": "slot", "kind": "expression"}}, {"name": "okButtonProps", "default": "-", "description": "The ok button props", "value": {"type": "[ButtonProps](/components/button/#api)", "kind": "expression"}}, {"name": "okText", "default": "`Confirm`", "description": "text of the Confirm button", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "okType", "default": "`primary`", "description": "Button `type` of the Confirm button", "value": {"type": "string", "kind": "expression"}}, {"name": "showCancel", "default": "true", "description": "Show cancel button", "value": {"type": "boolean", "kind": "expression"}}, {"name": "title", "default": "-", "description": "title of the confirmation box", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "description", "default": "-", "description": "The description of the confirmation box title", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "visible (v-model)", "default": "-", "description": "hide or show", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-popover", "slots": [{"name": "content", "description": "Content of the card"}, {"name": "title", "description": "Title of the card"}], "events": [], "attributes": [{"name": "content", "default": "-", "description": "Content of the card", "value": {"type": "string|slot|vNode", "kind": "expression"}}, {"name": "title", "default": "-", "description": "Title of the card", "value": {"type": "string|slot|VNode", "kind": "expression"}}]}, {"name": "a-progress", "slots": [], "events": [], "attributes": [{"name": "format", "default": "(percent) => percent + `%`", "description": "The template function of the content", "value": {"type": "function(percent, successPercent)", "kind": "expression"}}, {"name": "percent", "default": "0", "description": "To set the completion percentage", "value": {"type": "number", "kind": "expression"}}, {"name": "showInfo", "default": "true", "description": "Whether to display the progress value and the status icon", "value": {"type": "boolean", "kind": "expression"}}, {"name": "status", "default": "-", "description": "To set the status of the Progress, options: `success` `exception` `normal` `active`(line only)", "value": {"type": "string", "kind": "expression"}}, {"name": "strokeColor", "default": "-", "description": "The color of progress bar", "value": {"type": "string", "kind": "expression"}}, {"name": "strokeLinecap", "default": "`round`", "description": "To set the style of the progress linecap", "value": {"type": "`round` | `butt` | `square`, see [stroke-linecap](https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-linecap)", "kind": "expression"}}, {"name": "success", "default": "-", "description": "Configs of successfully progress bar", "value": {"type": "{ percent: number, strokeColor: string }", "kind": "expression"}}, {"name": "title", "default": "-", "description": "html dom title", "value": {"type": "string", "kind": "expression"}}, {"name": "trailColor", "default": "-", "description": "The color of unfilled part", "value": {"type": "string", "kind": "expression"}}, {"name": "type", "default": "`line`", "description": "To set the type, options: `line` `circle` `dashboard`", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "\"default\"", "description": "Progress size", "value": {"type": "number | [number, number] | \"small\" | \"default\"", "kind": "expression"}}]}, {"name": "a-qrcode", "slots": [], "events": [{"name": "refresh", "description": "callback"}], "attributes": [{"name": "value", "default": "-", "description": "scanned link", "value": {"type": "string", "kind": "expression"}}, {"name": "type", "default": "`canvas`", "description": "render type", "value": {"type": "`'canvas'` | `'svg'`", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "include image url (only image link are supported)", "value": {"type": "string", "kind": "expression"}}, {"name": "size", "default": "128", "description": "QRCode size", "value": {"type": "number", "kind": "expression"}}, {"name": "iconSize", "default": "32", "description": "include image size", "value": {"type": "number", "kind": "expression"}}, {"name": "color", "default": "`#000`", "description": "QRCode Color", "value": {"type": "string", "kind": "expression"}}, {"name": "bgColor", "default": "`transparent`", "description": "QRCode Background Color", "value": {"type": "string", "kind": "expression"}}, {"name": "bordered", "default": "`true`", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "errorLevel", "default": "`'M'`", "description": "Error Code Level", "value": {"type": "`'L'` | `'M'` | `'Q'` | `'H'`", "kind": "expression"}}, {"name": "status", "default": "`active`", "description": "QRCode status", "value": {"type": "`active` | `expired` | `loading` | `scanned`", "kind": "expression"}}]}, {"name": "a-radio", "slots": [], "events": [], "attributes": [{"name": "blur()", "description": "remove focus", "value": {"type": "", "kind": "expression"}}, {"name": "focus()", "description": "get focus", "value": {"type": "", "kind": "expression"}}]}, {"name": "a-radio-radio-button", "slots": [], "events": [], "attributes": [{"name": "autofocus", "default": "false", "description": "get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "checked(v-model)", "default": "-", "description": "Specifies whether the radio is selected.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disable radio", "value": {"type": "boolean", "kind": "expression"}}, {"name": "value", "default": "-", "description": "According to value for comparison, to determine whether the selected", "value": {"type": "any", "kind": "expression"}}]}, {"name": "a-radio-group", "slots": [], "events": [], "attributes": [{"name": "buttonStyle", "default": "`outline`", "description": "style type of radio button", "value": {"type": "`outline` | `solid`", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disable all radio buttons", "value": {"type": "boolean", "kind": "expression"}}, {"name": "name", "default": "-", "description": "The `name` property of all `input[type=\"radio\"]` children", "value": {"type": "string", "kind": "expression"}}, {"name": "options", "default": "-", "description": "set children optional", "value": {"type": "string[] | number[] | Array&lt;{ label: string value: string disabled?: boolean }>", "kind": "expression"}}, {"name": "optionType", "default": "`default`", "description": "Set Radio optionType", "value": {"type": "`default` | `button`", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "size for radio button style", "value": {"type": "`large` | `default` | `small`", "kind": "expression"}}, {"name": "value(v-model)", "default": "-", "description": "Used for setting the currently selected value.", "value": {"type": "any", "kind": "expression"}}, {"name": "change", "description": "The callback function that is triggered when the state changes.", "value": {"type": "Function(e:Event)", "kind": "expression"}}]}, {"name": "a-rate", "slots": [{"name": "character", "description": "custom character of rate"}], "events": [{"name": "blur", "description": "callback when component lose focus"}, {"name": "change", "description": "callback when select value"}, {"name": "focus", "description": "callback when component get focus"}, {"name": "hoverChange", "description": "callback when hover item"}, {"name": "keydown", "description": "callback when keydown on component"}], "attributes": [{"name": "allowClear", "default": "true", "description": "whether to allow clear when click again", "value": {"type": "boolean", "kind": "expression"}}, {"name": "allowHalf", "default": "false", "description": "whether to allow semi selection", "value": {"type": "boolean", "kind": "expression"}}, {"name": "autofocus", "default": "false", "description": "get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "character", "default": "`<StarOutlined />`", "description": "custom character of rate", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "count", "default": "5", "description": "star count", "value": {"type": "number", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "read only, unable to interact", "value": {"type": "boolean", "kind": "expression"}}, {"name": "tooltips", "default": "-", "description": "Customize tooltip by each character", "value": {"type": "string[]", "kind": "expression"}}, {"name": "value(v-model)", "default": "-", "description": "current value", "value": {"type": "number", "kind": "expression"}}]}, {"name": "a-result", "slots": [{"name": "extra", "description": "operating area"}, {"name": "icon", "description": "custom back icon"}, {"name": "subTitle", "description": "subTitle string"}, {"name": "title", "description": "title string"}], "events": [], "attributes": [{"name": "extra", "default": "-", "description": "operating area", "value": {"type": "slot", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "custom back icon", "value": {"type": "slot", "kind": "expression"}}, {"name": "status", "default": "'info'", "description": "result status,decide icons and colors", "value": {"type": "`success` | `error` | `info` | `warning` | `404` | `403` | `500`", "kind": "expression"}}, {"name": "subTitle", "default": "-", "description": "subTitle string", "value": {"type": "string | VNode | slot", "kind": "expression"}}, {"name": "title", "default": "-", "description": "title string", "value": {"type": "string | VNode | slot", "kind": "expression"}}]}, {"name": "a-segmented", "slots": [{"name": "label", "description": "custom label by slot"}], "events": [{"name": "change", "description": "The callback function that is triggered when the state changes"}], "attributes": [{"name": "block", "default": "false", "description": "Option to fit width to its parent\\'s width", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disable all segments", "value": {"type": "boolean", "kind": "expression"}}, {"name": "options", "default": "[]", "description": "Set children optional", "value": {"type": "string[] | number[] | SegmentedOption[]", "kind": "expression"}}, {"name": "size", "default": "-", "description": "The size of the Segmented.", "value": {"type": "`large` | `middle` | `small`", "kind": "expression"}}, {"name": "value", "default": "", "description": "Currently selected value", "value": {"type": "string | number", "kind": "expression"}}, {"name": "label", "default": "", "description": "custom label by slot", "value": {"type": "v-slot:label=\"SegmentedBaseOption\"", "kind": "expression"}}]}, {"name": "a-select", "slots": [{"name": "clearIcon", "description": "The custom clear icon"}, {"name": "dropdownRender", "description": "Customize dropdown content"}, {"name": "maxTagPlaceholder", "description": "Placeholder for not showing tags"}, {"name": "menuItemSelectedIcon", "description": "The custom menuItemSelected icon"}, {"name": "notFoundContent", "description": "Specify content to show when no result matches.."}, {"name": "option", "description": "custom render option by slot"}, {"name": "placeholder", "description": "Placeholder of select"}, {"name": "removeIcon", "description": "The custom remove icon"}, {"name": "suffixIcon", "description": "The custom suffix icon"}, {"name": "tagRender", "description": "Customize tag render, only applies when `mode` is set to `multiple` or `tags`"}], "events": [{"name": "blur", "description": "Called when blur"}, {"name": "change", "description": "Called when select an option or input value change, or value of input is changed in combobox mode"}, {"name": "deselect", "description": "Called when a option is deselected, the params are option's value (or key) . only called for multiple or tags, effective in multiple or tags mode only."}, {"name": "dropdownVisibleChange", "description": "Call when dropdown open"}, {"name": "focus", "description": "Called when focus"}, {"name": "inputKeyDown", "description": "Called when key pressed"}, {"name": "mouseenter", "description": "Called when mouse enter"}, {"name": "mouseleave", "description": "Called when mouse leave"}, {"name": "popupScroll", "description": "Called when dropdown scrolls"}, {"name": "search", "description": "Callback function that is fired when input changed."}, {"name": "select", "description": "Called when a option is selected, the params are option's value (or key) and option instance."}], "attributes": [{"name": "allowClear", "default": "false", "description": "Show clear button.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "autoClearSearchValue", "default": "true", "description": "Whether the current search will be cleared on selecting an item. Only applies when `mode` is set to `multiple` or `tags`.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "autofocus", "default": "false", "description": "Get focus by default", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bordered", "default": "true", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clearIcon", "default": "-", "description": "The custom clear icon", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "defaultActiveFirstOption", "default": "true", "description": "Whether active first option by default", "value": {"type": "boolean", "kind": "expression"}}, {"name": "defaultOpen", "default": "-", "description": "Initial open state of dropdown", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Whether disabled select", "value": {"type": "boolean", "kind": "expression"}}, {"name": "popupClassName", "default": "-", "description": "className of dropdown menu", "value": {"type": "string", "kind": "expression"}}, {"name": "dropdownMatchSelectWidth", "default": "true", "description": "Determine whether the dropdown menu and the select input are the same width. <PERSON><PERSON><PERSON> set `min-width` same as input. Will ignore when value less than select width. `false` will disable virtual scroll", "value": {"type": "boolean | number", "kind": "expression"}}, {"name": "dropdownMenuStyle", "default": "-", "description": "additional style applied to dropdown menu", "value": {"type": "object", "kind": "expression"}}, {"name": "dropdownRender", "default": "-", "description": "Customize dropdown content", "value": {"type": "({menuNode: VNode, props}) => VNode | v-slot", "kind": "expression"}}, {"name": "dropdownStyle", "default": "-", "description": "style of dropdown menu", "value": {"type": "object", "kind": "expression"}}, {"name": "fieldNames", "default": "{ label: `label`, value: `value`, options: `options` }", "description": "Customize node label, value, options field name", "value": {"type": "object", "kind": "expression"}}, {"name": "filterOption", "default": "true", "description": "If true, filter options by input, if function, filter options against it. The function will receive two arguments, `inputValue` and `option`, if the function returns `true`, the option will be included in the filtered set; Otherwise, it will be excluded.", "value": {"type": "`boolean` | `function(inputValue, option)`", "kind": "expression"}}, {"name": "filterSort", "default": "-", "description": "Sort function for search options sorting, see [Array.sort](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort)'s compareFunction", "value": {"type": "(optionA: Option, optionB: Option) => number", "kind": "expression"}}, {"name": "firstActiveValue", "default": "-", "description": "Value of action option by default", "value": {"type": "string | string[]", "kind": "expression"}}, {"name": "getPopupContainer", "default": "() => document.body", "description": "Parent Node which the selector should be rendered to. Default to `body`. When position issues happen, try to modify it into scrollable content and position it relative.", "value": {"type": "function(triggerNode)", "kind": "expression"}}, {"name": "labelInValue", "default": "false", "description": "whether to embed label in value, turn the format of value from `string` to `{key: string, label: vNodes, originLabel: any}`, originLabel (3.1) maintains the original type. If the node is constructed through a-select-option children, the value is a function (the default slot of a-select-option)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "listHeight", "default": "256", "description": "Config popup height", "value": {"type": "number", "kind": "expression"}}, {"name": "loading", "default": "false", "description": "indicate loading state", "value": {"type": "boolean", "kind": "expression"}}, {"name": "maxTag<PERSON>ount", "default": "-", "description": "Max tag count to show", "value": {"type": "number", "kind": "expression"}}, {"name": "maxTagPlaceholder", "default": "-", "description": "Placeholder for not showing tags", "value": {"type": "slot | function(omittedValues)", "kind": "expression"}}, {"name": "maxTagTextLength", "default": "-", "description": "Max text length to show", "value": {"type": "number", "kind": "expression"}}, {"name": "menuItemSelectedIcon", "default": "-", "description": "The custom menuItemSelected icon", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "mode", "default": "-", "description": "Set mode of Select", "value": {"type": "'multiple' | 'tags'", "kind": "expression"}}, {"name": "notFoundContent", "default": "`Not Found`", "description": "Specify content to show when no result matches..", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "open", "default": "-", "description": "Controlled open state of dropdown", "value": {"type": "boolean", "kind": "expression"}}, {"name": "option", "default": "-", "description": "custom render option by slot", "value": {"type": "v-slot:option=\"{value, label, [disabled, key, title]}\"", "kind": "expression"}}, {"name": "optionFilterProp", "default": "value", "description": "Which prop value of option will be used for filter if filterOption is true", "value": {"type": "string", "kind": "expression"}}, {"name": "optionLabelProp", "default": "`children` | `label`(when use options)", "description": "Which prop value of option will render as content of select.", "value": {"type": "string", "kind": "expression"}}, {"name": "options", "default": "\\[]", "description": "Data of the selectOption, manual construction work is no longer needed if this property has been set", "value": {"type": "Array&lt;{value, label, [disabled, key, title]}>", "kind": "expression"}}, {"name": "placeholder", "default": "-", "description": "Placeholder of select", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "placement", "default": "bottomLeft", "description": "The position where the selection box pops up", "value": {"type": "`bottomLeft` `bottomRight` `topLeft` `topRight`", "kind": "expression"}}, {"name": "removeIcon", "default": "-", "description": "The custom remove icon", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "searchValue", "default": "-", "description": "The current input \"search\" text", "value": {"type": "string", "kind": "expression"}}, {"name": "showArrow", "default": "single:true, multiple:false", "description": "Whether to show the drop-down arrow", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showSearch", "default": "single:false, multiple:true", "description": "Whether select is searchable", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "default", "description": "Size of Select input. `default` `large` `small`", "value": {"type": "string", "kind": "expression"}}, {"name": "status", "default": "-", "description": "Set validation status", "value": {"type": "'error' | 'warning'", "kind": "expression"}}, {"name": "suffixIcon", "default": "-", "description": "The custom suffix icon", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "tagRender", "default": "-", "description": "Customize tag render, only applies when `mode` is set to `multiple` or `tags`", "value": {"type": "slot | (props) => any", "kind": "expression"}}, {"name": "tokenSeparators", "default": "-", "description": "Separator used to tokenize, only applies when `mode=\"tags\"`", "value": {"type": "string[]", "kind": "expression"}}, {"name": "value(v-model)", "default": "-", "description": "Current selected option.", "value": {"type": "string|number|string[]|number[]", "kind": "expression"}}, {"name": "virtual", "default": "true", "description": "Disable virtual scroll when set to false", "value": {"type": "boolean", "kind": "expression"}}, {"name": "blur()", "description": "Remove focus", "value": {"type": "", "kind": "expression"}}, {"name": "focus()", "description": "Get focus", "value": {"type": "", "kind": "expression"}}]}, {"name": "a-skeleton", "slots": [], "events": [], "attributes": [{"name": "active", "default": "false", "description": "Show animation effect", "value": {"type": "boolean", "kind": "expression"}}, {"name": "avatar", "default": "false", "description": "Show avatar placeholder", "value": {"type": "boolean | [SkeletonAvatarProps](#skeletonavatarprops)", "kind": "expression"}}, {"name": "loading", "default": "-", "description": "Display the skeleton when `true`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "paragraph", "default": "true", "description": "Show paragraph placeholder", "value": {"type": "boolean | [SkeletonParagraphProps](#skeletonparagraphprops)", "kind": "expression"}}, {"name": "title", "default": "true", "description": "Show title placeholder", "value": {"type": "boolean | [SkeletonTitleProps](#skeletontitleprops)", "kind": "expression"}}]}, {"name": "a-skeleton-avatar-props", "slots": [], "events": [], "attributes": [{"name": "shape", "default": "-", "description": "Set the shape of avatar", "value": {"type": "`circle` | `square`", "kind": "expression"}}, {"name": "size", "default": "-", "description": "Set the size of avatar", "value": {"type": "number | `large` | `small` | `default`", "kind": "expression"}}]}, {"name": "a-skeleton-title-props", "slots": [], "events": [], "attributes": [{"name": "width", "default": "-", "description": "Set the width of title", "value": {"type": "number | string", "kind": "expression"}}]}, {"name": "a-skeleton-paragraph-props", "slots": [], "events": [], "attributes": [{"name": "rows", "default": "-", "description": "Set the row count of paragraph", "value": {"type": "number", "kind": "expression"}}, {"name": "width", "default": "-", "description": "Set the width of paragraph. When width is an Array, it can set the width of each row. Otherwise only set the last row width", "value": {"type": "number | string | Array&lt;number | string>", "kind": "expression"}}]}, {"name": "a-skeleton-button-props", "slots": [], "events": [], "attributes": [{"name": "active", "default": "false", "description": "Show animation effect", "value": {"type": "boolean", "kind": "expression"}}, {"name": "block", "default": "false", "description": "Option to fit button width to its parent width", "value": {"type": "boolean", "kind": "expression"}}, {"name": "shape", "default": "-", "description": "Set the shape of button", "value": {"type": "`circle` | `round` | `default`", "kind": "expression"}}, {"name": "size", "default": "-", "description": "Set the size of button", "value": {"type": "`large` | `small` | `default`", "kind": "expression"}}]}, {"name": "a-skeleton-input-props", "slots": [], "events": [], "attributes": [{"name": "active", "default": "false", "description": "Show animation effect", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "-", "description": "Set the size of input", "value": {"type": "`large` | `small` | `default`", "kind": "expression"}}]}, {"name": "a-slider", "slots": [{"name": "mark", "description": "Custom tick mark of Slider,"}], "events": [{"name": "change", "description": "Callback function that is fired when the user changes the slider's value."}, {"name": "afterChange", "description": "Fire when `mouseup` is fired."}], "attributes": [{"name": "autofocus", "default": "false", "description": "get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "If true, the slider will not be intractable.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "dots", "default": "false", "description": "Whether the thumb can drag over tick only.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "handleStyle", "default": "-", "description": "The style of slider handle", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "included", "default": "true", "description": "Make effect when `marks` not null, `true` means containment and `false` means coordinative", "value": {"type": "boolean", "kind": "expression"}}, {"name": "mark", "default": "{ point: number, label: any }", "description": "Custom tick mark of Slider,", "value": {"type": "v-slot:mark", "kind": "expression"}}, {"name": "marks", "default": "{ number: string|VNode } or { number: { style: object, label: string|VNode } } or { number: () => VNode }", "description": "Tick mark of Slider, type of key must be `number`, and must in closed interval \\[min, max], each mark can declare its own style.", "value": {"type": "object", "kind": "expression"}}, {"name": "max", "default": "100", "description": "The maximum value the slider can slide to", "value": {"type": "number", "kind": "expression"}}, {"name": "min", "default": "0", "description": "The minimum value the slider can slide to.", "value": {"type": "number", "kind": "expression"}}, {"name": "range", "default": "false", "description": "dual thumb mode", "value": {"type": "boolean", "kind": "expression"}}, {"name": "reverse", "default": "false", "description": "reverse the component", "value": {"type": "boolean", "kind": "expression"}}, {"name": "step", "default": "1", "description": "The granularity the slider can step through values. Must greater than 0, and be divided by (max - min) . When `marks` no null, `step` can be `null`.", "value": {"type": "number|null", "kind": "expression"}}, {"name": "trackStyle", "default": "-", "description": "The style of slider track", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "value(v-model)", "default": "", "description": "The value of slider. When `range` is `false`, use `number`, otherwise, use `[number, number]`", "value": {"type": "number|number[]", "kind": "expression"}}, {"name": "vertical", "default": "false", "description": "If true, the slider will be vertical.", "value": {"type": "Boolean", "kind": "expression"}}, {"name": "tip<PERSON><PERSON><PERSON><PERSON>", "default": "IDENTITY", "description": "Slider will pass its value to `tip<PERSON><PERSON>att<PERSON>`, and display its value in Tooltip, and hide Tooltip when return value is null.", "value": {"type": "Function|null", "kind": "expression"}}, {"name": "tooltipPlacement", "default": "", "description": "Set Tooltip display position. Ref [`Tooltip`](/components/tooltip/).", "value": {"type": "string", "kind": "expression"}}, {"name": "tooltipOpen", "default": "", "description": "If true, <PERSON><PERSON><PERSON> will show always, or it will not show anyway, even if dragging or hovering.", "value": {"type": "Boolean", "kind": "expression"}}, {"name": "getTooltipPopupContainer", "default": "() => document.body", "description": "The DOM container of the Tooltip, the default behavior is to create a div element in body.", "value": {"type": "Function", "kind": "expression"}}]}, {"name": "a-space", "slots": [{"name": "split", "description": "Set split"}], "events": [], "attributes": [{"name": "align", "default": "-", "description": "Align items", "value": {"type": "`start` | `end` |`center` |`baseline`", "kind": "expression"}}, {"name": "direction", "default": "`horizontal`", "description": "The space direction", "value": {"type": "`vertical` | `horizontal`", "kind": "expression"}}, {"name": "size", "default": "`small`", "description": "The space size", "value": {"type": "`small` | `middle` | `large` | `number`", "kind": "expression"}}, {"name": "split", "default": "-", "description": "Set split", "value": {"type": "VueNode | v-slot", "kind": "expression"}}, {"name": "wrap", "default": "false", "description": "Auto wrap line, when `horizontal` effective", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-space-compact", "slots": [], "events": [], "attributes": [{"name": "block", "default": "false", "description": "Option to fit width to its parent\\'s width", "value": {"type": "boolean", "kind": "expression"}}, {"name": "direction", "default": "`horizontal`", "description": "Set direction of layout", "value": {"type": "`vertical` | `horizontal`", "kind": "expression"}}, {"name": "size", "default": "`middle`", "description": "Set child component size", "value": {"type": "`large` | `middle` | `small`", "kind": "expression"}}]}, {"name": "a-spin", "slots": [{"name": "indicator", "description": "vue node of the spinning indicator"}, {"name": "tip", "description": "customize description content when <PERSON> has children"}], "events": [], "attributes": [{"name": "delay", "default": "-", "description": "specifies a delay in milliseconds for loading state (prevent flush)", "value": {"type": "number (milliseconds)", "kind": "expression"}}, {"name": "indicator", "default": "-", "description": "vue node of the spinning indicator", "value": {"type": "vNode |slot", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "size of Spin, options: `small`, `default` and `large`", "value": {"type": "string", "kind": "expression"}}, {"name": "spinning", "default": "true", "description": "whether Spin is visible", "value": {"type": "boolean", "kind": "expression"}}, {"name": "tip", "default": "-", "description": "customize description content when <PERSON> has children", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "wrapperClassName", "default": "-", "description": "className of wrapper when <PERSON> has children", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-statistic", "slots": [{"name": "formatter", "description": "customize value display logic"}, {"name": "prefix", "description": "prefix node of value"}, {"name": "suffix", "description": "suffix node of value"}, {"name": "title", "description": "Display title"}], "events": [], "attributes": [{"name": "decimalSeparator", "default": ".", "description": "decimal separator", "value": {"type": "string", "kind": "expression"}}, {"name": "formatter", "default": "-", "description": "customize value display logic", "value": {"type": "v-slot |({value}) => VNode", "kind": "expression"}}, {"name": "groupSeparator", "default": ",", "description": "group separator", "value": {"type": "string", "kind": "expression"}}, {"name": "precision", "default": "-", "description": "precision of input value", "value": {"type": "number", "kind": "expression"}}, {"name": "prefix", "default": "-", "description": "prefix node of value", "value": {"type": "string | v-slot", "kind": "expression"}}, {"name": "suffix", "default": "-", "description": "suffix node of value", "value": {"type": "string | v-slot", "kind": "expression"}}, {"name": "title", "default": "-", "description": "Display title", "value": {"type": "string | v-slot", "kind": "expression"}}, {"name": "value", "default": "-", "description": "Display value", "value": {"type": "string | number", "kind": "expression"}}, {"name": "valueStyle", "default": "-", "description": "Set value css style", "value": {"type": "style", "kind": "expression"}}]}, {"name": "a-statistic-countdown", "slots": [{"name": "prefix", "description": "prefix node of value"}, {"name": "suffix", "description": "suffix node of value"}, {"name": "title", "description": "Display title"}], "events": [], "attributes": [{"name": "format", "default": "'HH:mm:ss'", "description": "Format as [dayjs](https://day.js.org/)", "value": {"type": "string", "kind": "expression"}}, {"name": "prefix", "default": "-", "description": "prefix node of value", "value": {"type": "string | v-slot", "kind": "expression"}}, {"name": "suffix", "default": "-", "description": "suffix node of value", "value": {"type": "string | v-slot", "kind": "expression"}}, {"name": "title", "default": "-", "description": "Display title", "value": {"type": "string | v-slot", "kind": "expression"}}, {"name": "value", "default": "-", "description": "Set target countdown time", "value": {"type": "number | dayjs", "kind": "expression"}}, {"name": "valueStyle", "default": "-", "description": "Set value css style", "value": {"type": "style", "kind": "expression"}}, {"name": "finish", "default": "-", "description": "Trigger when time's up", "value": {"type": "() => void", "kind": "expression"}}]}, {"name": "a-steps", "slots": [{"name": "progressDot", "description": "Steps with progress dot style, customize the progress dot by setting a scoped slot. labelPlacement will be `vertical`"}], "events": [], "attributes": [{"name": "current(v-model)", "default": "0", "description": "to set the current step, counting from 0. You can overwrite this state by using `status` of `Step`, support v-model after 1.5.0", "value": {"type": "number", "kind": "expression"}}, {"name": "direction", "default": "`horizontal`", "description": "to specify the direction of the step bar, `horizontal` and `vertical` are currently supported", "value": {"type": "string", "kind": "expression"}}, {"name": "initial", "default": "0", "description": "set the initial step, counting from 0", "value": {"type": "number", "kind": "expression"}}, {"name": "labelPlacement", "default": "`horizontal`", "description": "support vertical title and description", "value": {"type": "string", "kind": "expression"}}, {"name": "percent", "default": "-", "description": "Progress circle percentage of current step in `process` status (only works on basic Steps)", "value": {"type": "number", "kind": "expression"}}, {"name": "progressDot", "default": "false", "description": "Steps with progress dot style, customize the progress dot by setting a scoped slot. labelPlacement will be `vertical`", "value": {"type": "Boolean or v-slot:progressDot=\"{index, status, title, description, prefixCls, iconDot}\"", "kind": "expression"}}, {"name": "responsive", "default": "true", "description": "change to vertical direction when screen width smaller than `532px`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "`default`", "description": "to specify the size of the step bar, `default` and `small` are currently supported", "value": {"type": "string", "kind": "expression"}}, {"name": "status", "default": "`process`", "description": "to specify the status of current step, can be set to one of the following values: `wait` `process` `finish` `error`", "value": {"type": "string", "kind": "expression"}}, {"name": "type", "default": "`default`", "description": "Type of steps, can be set to one of the following values: `default`, `navigation`", "value": {"type": "string", "kind": "expression"}}, {"name": "items", "default": "[]", "description": "StepItem content", "value": {"type": "[StepItem](#stepsstep)", "kind": "expression"}}, {"name": "change", "default": "-", "description": "Trigger when Step is changed", "value": {"type": "(current) => void", "kind": "expression"}}]}, {"name": "a-steps-step", "slots": [{"name": "description", "description": "description of the step, optional property"}, {"name": "icon", "description": "icon of the step, optional property"}, {"name": "subTitle", "description": "Subtitle of the step"}, {"name": "title", "description": "title of the step"}], "events": [], "attributes": [{"name": "description", "default": "-", "description": "description of the step, optional property", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disable click", "value": {"type": "boolean", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "icon of the step, optional property", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "status", "default": "`wait`", "description": "to specify the status. It will be automatically set by `current` of `Steps` if not configured. Optional values are: `wait` `process` `finish` `error`", "value": {"type": "string", "kind": "expression"}}, {"name": "subTitle", "default": "-", "description": "Subtitle of the step", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "title", "default": "-", "description": "title of the step", "value": {"type": "string|slot", "kind": "expression"}}]}, {"name": "a-switch", "slots": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "content to be shown when the state is checked"}, {"name": "unChecked<PERSON><PERSON><PERSON>n", "description": "content to be shown when the state is unchecked"}], "events": [], "attributes": [{"name": "autofocus", "default": "false", "description": "get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "checked(v-model)", "default": "false", "description": "determine whether the `Switch` is checked", "value": {"type": "checkedValue | unCheckedValue", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "default": "", "description": "content to be shown when the state is checked", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "checkedValue", "default": "true", "description": "value for checked state", "value": {"type": "boolean | string | number", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disable switch", "value": {"type": "boolean", "kind": "expression"}}, {"name": "loading", "default": "false", "description": "loading state of switch", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "default", "description": "the size of the `Switch`, options: `default` `small`", "value": {"type": "string", "kind": "expression"}}, {"name": "unChecked<PERSON><PERSON><PERSON>n", "default": "", "description": "content to be shown when the state is unchecked", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "unCheckedValue", "default": "false", "description": "value for unchecked state", "value": {"type": "boolean | string | number", "kind": "expression"}}]}, {"name": "a-table", "slots": [{"name": "bodyCell", "description": "custom body cell by slot"}, {"name": "customFilterDropdown", "description": "Customized filter overlay, need set `column.customFilterDropdown`"}, {"name": "customFilterIcon", "description": "Customized filter icon"}, {"name": "emptyText", "description": "Customize the display content when empty data"}, {"name": "expandedRowRender", "description": "Expanded container render for each row"}, {"name": "expandColumnTitle", "description": "Set the title of the expand column"}, {"name": "expandIcon", "description": "Customize row expand Icon."}, {"name": "footer", "description": "Table footer renderer"}, {"name": "headerCell", "description": "custom head cell by slot"}, {"name": "summary", "description": "Summary content"}, {"name": "title", "description": "Table title renderer"}], "events": [], "attributes": [{"name": "bodyCell", "default": "-", "description": "custom body cell by slot", "value": {"type": "v-slot:bodyCell=\"{text, record, index, column}\"", "kind": "expression"}}, {"name": "bordered", "default": "`false`", "description": "Whether to show all table borders", "value": {"type": "boolean", "kind": "expression"}}, {"name": "childrenColumnName", "default": "`children`", "description": "The column contains children to display", "value": {"type": "string", "kind": "expression"}}, {"name": "columns", "default": "-", "description": "Columns of table [config](#column)", "value": {"type": "array", "kind": "expression"}}, {"name": "components", "default": "-", "description": "Override default table elements", "value": {"type": "object", "kind": "expression"}}, {"name": "customFilterDropdown", "default": "-", "description": "Customized filter overlay, need set `column.customFilterDropdown`", "value": {"type": "v-slot:customFilterDropdown=\"[FilterDropdownProps](#filterdropdownprops)\"", "kind": "expression"}}, {"name": "customFilterIcon", "default": "-", "description": "Customized filter icon", "value": {"type": "v-slot:customFilterIcon=\"{filtered, column}\"", "kind": "expression"}}, {"name": "customHeaderRow", "default": "-", "description": "Set props on per header row", "value": {"type": "Function(columns, index)", "kind": "expression"}}, {"name": "customRow", "default": "-", "description": "Set props on per row", "value": {"type": "Function(record, index)", "kind": "expression"}}, {"name": "dataSource", "default": "-", "description": "Data record array to be displayed", "value": {"type": "any[]", "kind": "expression"}}, {"name": "defaultExpandAllRows", "default": "`false`", "description": "Expand all rows initially", "value": {"type": "boolean", "kind": "expression"}}, {"name": "defaultExpandedRowKeys", "default": "-", "description": "Initial expanded row keys", "value": {"type": "string[]", "kind": "expression"}}, {"name": "emptyText", "default": "-", "description": "Customize the display content when empty data", "value": {"type": "v-slot:emptyText", "kind": "expression"}}, {"name": "expandedRowKeys(v-model)", "default": "-", "description": "Current expanded row keys", "value": {"type": "string[]", "kind": "expression"}}, {"name": "expandedRowRender", "default": "-", "description": "Expanded container render for each row", "value": {"type": "Function({record, index, indent, expanded}):VNode|v-slot", "kind": "expression"}}, {"name": "expandFixed", "default": "false", "description": "Set column to be fixed: `true`(same as left) `'left'` `'right'`", "value": {"type": "boolean | string", "kind": "expression"}}, {"name": "expandColumnTitle", "default": "-", "description": "Set the title of the expand column", "value": {"type": "v-slot", "kind": "expression"}}, {"name": "expandIcon", "default": "-", "description": "Customize row expand Icon.", "value": {"type": "Function(props):VNode | v-slot:expandIcon=\"props\"", "kind": "expression"}}, {"name": "expandRowByClick", "default": "`false`", "description": "Whether to expand row by clicking anywhere in the whole row", "value": {"type": "boolean", "kind": "expression"}}, {"name": "footer", "default": "", "description": "Table footer renderer", "value": {"type": "Function(currentPageData)| v-slot:footer=\"currentPageData\"", "kind": "expression"}}, {"name": "getPopupContainer", "default": "`() => TableHtmlElement`", "description": "the render container of dropdowns in table", "value": {"type": "(triggerNode) => HTMLElement", "kind": "expression"}}, {"name": "headerCell", "default": "-", "description": "custom head cell by slot", "value": {"type": "v-slot:headerCell=\"{title, column}\"", "kind": "expression"}}, {"name": "indentSize", "default": "15", "description": "Indent size in pixels of tree data", "value": {"type": "number", "kind": "expression"}}, {"name": "loading", "default": "`false`", "description": "Loading status of table", "value": {"type": "boolean|[object](/components/spin)", "kind": "expression"}}, {"name": "locale", "default": "filterConfirm: 'Ok' <br /> filterReset: 'Reset' <br /> emptyText: 'No Data'", "description": "i18n text including filter, sort, empty text, etc", "value": {"type": "object", "kind": "expression"}}, {"name": "pagination", "default": "", "description": "Config of pagination. You can ref table pagination [config](#pagination) or full [`pagination`](/components/pagination/) document, hide it by setting it to `false`", "value": {"type": "object | `false`", "kind": "expression"}}, {"name": "rowClassName", "default": "-", "description": "Row's className", "value": {"type": "Function(record, index):string", "kind": "expression"}}, {"name": "rowExpandable", "default": "-", "description": "Enable row can be expandable", "value": {"type": "(record) => boolean", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "default": "`key`", "description": "<PERSON>'s unique key, could be a string or function that returns a string", "value": {"type": "string|Function(record, index):string", "kind": "expression"}}, {"name": "rowSelection", "default": "null", "description": "Row selection [config](#rowselection)", "value": {"type": "object", "kind": "expression"}}, {"name": "scroll", "default": "-", "description": "Whether the table can be scrollable, [config](#scroll)", "value": {"type": "object", "kind": "expression"}}, {"name": "showExpandColumn", "default": "true", "description": "Show expand column", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showHeader", "default": "`true`", "description": "Whether to show table header", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showSorterTooltip", "default": "true", "description": "The header show next sorter direction tooltip. It will be set as the property of Tooltip if its type is object", "value": {"type": "boolean | [Tooltip props](/components/tooltip/#api)", "kind": "expression"}}, {"name": "size", "default": "`large`", "description": "Size of table", "value": {"type": "`middle` | `small` | `large`", "kind": "expression"}}, {"name": "sortDirections", "default": "\\[`ascend`, `descend`]", "description": "Supported sort way, could be `ascend`, `descend`", "value": {"type": "Array", "kind": "expression"}}, {"name": "sticky", "default": "-", "description": "Set sticky header and scroll bar", "value": {"type": "boolean | `{offsetHeader?: number, offsetScroll?: number, getContainer?: () => HTMLElement}`", "kind": "expression"}}, {"name": "summary", "default": "-", "description": "Summary content", "value": {"type": "v-slot:summary", "kind": "expression"}}, {"name": "tableLayout", "default": "-<hr />`fixed` when header/columns are fixed, or using `column.ellipsis`", "description": "[table-layout](https://developer.mozilla.org/en-US/docs/Web/CSS/table-layout) attribute of table element", "value": {"type": "- | 'auto' | 'fixed'", "kind": "expression"}}, {"name": "title", "default": "", "description": "Table title renderer", "value": {"type": "Function(currentPageData)| v-slot:title=\"currentPageData\"", "kind": "expression"}}, {"name": "transformCellText", "default": "-", "description": "The data can be changed again before rendering, generally used for the default configuration of empty data. You can configured globally through [ConfigProvider](/components/config-provider-cn/)", "value": {"type": "Function({ text, column, record, index }) => any, The `text` here is the data processed by other defined cell api, and it may be of type VNode | string | number", "kind": "expression"}}]}, {"name": "a-tabs", "slots": [], "events": [], "attributes": [{"name": "<PERSON><PERSON><PERSON>(v-model)", "default": "-", "description": "Current TabPane's key", "value": {"type": "string", "kind": "expression"}}, {"name": "animated", "default": "`true`, `false` when `type=\"card\"`", "description": "Whether to change tabs with animation. Only works while tabPosition=`\"top\"` | `\"bottom\"`", "value": {"type": "boolean | {inkBar:boolean, tabPane:boolean}", "kind": "expression"}}, {"name": "destroyInactiveTabPane", "default": "false", "description": "Whether destroy inactive TabPane when change tab", "value": {"type": "boolean", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "default": "`false`", "description": "Hide plus icon or not. Only works while `type=\"editable-card\"`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "size", "default": "`middle`", "description": "preset tab bar size", "value": {"type": "`large` | `middle` | `small`", "kind": "expression"}}, {"name": "tabBarGutter", "default": "-", "description": "The gap between tabs", "value": {"type": "number", "kind": "expression"}}, {"name": "tabBarStyle", "default": "-", "description": "Tab bar style object", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "tabPosition", "default": "`top`", "description": "Position of tabs", "value": {"type": "`top` | `right` | `bottom` | `left`", "kind": "expression"}}, {"name": "type", "default": "`line`", "description": "Basic style of tabs", "value": {"type": "`line` | `card` | `editable-card`", "kind": "expression"}}, {"name": "addIcon", "default": "-", "description": "Customize add icon", "value": {"type": "-", "kind": "expression"}}, {"name": "leftExtra", "default": "-", "description": "Extra content in tab bar left", "value": {"type": "-", "kind": "expression"}}, {"name": "moreIcon", "default": "-", "description": "The custom icon of ellipsis", "value": {"type": "-", "kind": "expression"}}, {"name": "renderTabBar", "default": "", "description": "Replace the TabBar", "value": {"type": "{ DefaultTabBar }", "kind": "expression"}}, {"name": "rightExtra", "default": "-", "description": "Extra content in tab bar right", "value": {"type": "-", "kind": "expression"}}, {"name": "change", "description": "Callback executed when active tab is changed", "value": {"type": "Function(activeKey) {}", "kind": "expression"}}, {"name": "edit", "description": "Callback executed when tab is added or removed. Only works while `type=\"editable-card\"`", "value": {"type": "(action === 'add' ? event : target<PERSON>ey, action): void", "kind": "expression"}}, {"name": "nextClick", "description": "Callback executed when next button is clicked", "value": {"type": "Function", "kind": "expression"}}, {"name": "prevClick", "description": "Callback executed when prev button is clicked", "value": {"type": "Function", "kind": "expression"}}, {"name": "tabClick", "description": "Callback executed when tab is clicked", "value": {"type": "Function", "kind": "expression"}}]}, {"name": "a-tabs-tab-pane", "slots": [{"name": "tab", "description": "Show text in <PERSON><PERSON><PERSON><PERSON>'s head"}], "events": [], "attributes": [{"name": "forceRender", "default": "false", "description": "Forced render of content in tabs, not lazy render after clicking on tabs", "value": {"type": "boolean", "kind": "expression"}}, {"name": "key", "default": "-", "description": "<PERSON><PERSON><PERSON><PERSON>'s key", "value": {"type": "string", "kind": "expression"}}, {"name": "tab", "default": "-", "description": "Show text in <PERSON><PERSON><PERSON><PERSON>'s head", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "closeIcon", "description": "Customize close icon, Only works while `type=\"editable-card\"`", "value": {"type": "-", "kind": "expression"}}, {"name": "tab", "description": "Show text in <PERSON><PERSON><PERSON><PERSON>'s head", "value": {"type": "-", "kind": "expression"}}]}, {"name": "a-tag", "slots": [{"name": "closeIcon", "description": "Custom close icon"}, {"name": "icon", "description": "Set the icon of tag"}], "events": [], "attributes": [{"name": "closable", "default": "`false`", "description": "Whether the Tag can be closed", "value": {"type": "boolean", "kind": "expression"}}, {"name": "closeIcon", "default": "-", "description": "Custom close icon", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "color", "default": "-", "description": "Color of the Tag", "value": {"type": "string", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "Set the icon of tag", "value": {"type": "VNode | slot", "kind": "expression"}}, {"name": "bordered", "default": "`true`", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close", "description": "Callback executed when tag is closed", "value": {"type": "(e) => void", "kind": "expression"}}]}, {"name": "a-tag-checkable-tag", "slots": [], "events": [], "attributes": [{"name": "checked(v-model)", "default": "`false`", "description": "Checked status of Tag", "value": {"type": "boolean", "kind": "expression"}}, {"name": "change", "description": "Callback executed when Tag is checked/unchecked", "value": {"type": "(checked) => void", "kind": "expression"}}]}, {"name": "a-time-picker", "slots": [{"name": "clearIcon", "description": "The custom clear icon"}, {"name": "renderExtraFooter", "description": "Called from time picker panel to render some addon to its bottom"}, {"name": "suffixIcon", "description": "The custom suffix icon"}], "events": [{"name": "change", "description": "a callback function, can be executed when the selected time is changing"}, {"name": "openChange", "description": "a callback function which will be called while panel opening/closing"}], "attributes": [{"name": "allowClear", "default": "true", "description": "Whether allow clearing text", "value": {"type": "boolean", "kind": "expression"}}, {"name": "autofocus", "default": "false", "description": "If get focus when component mounted", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bordered", "default": "true", "description": "Whether has border style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "clearIcon", "default": "-", "description": "The custom clear icon", "value": {"type": "v-slot:clearIcon", "kind": "expression"}}, {"name": "clearText", "default": "clear", "description": "The clear tooltip of icon", "value": {"type": "string", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Determine whether the TimePicker is disabled", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabledTime", "default": "-", "description": "To specify the time that cannot be selected", "value": {"type": "[DisabledTime](#disabledtime)", "kind": "expression"}}, {"name": "format", "default": "`HH:mm:ss`", "description": "To set the time format", "value": {"type": "string", "kind": "expression"}}, {"name": "getPopupContainer", "default": "-", "description": "To set the container of the floating layer, while the default is to create a div element in body", "value": {"type": "function(trigger)", "kind": "expression"}}, {"name": "hideDisabledOptions", "default": "false", "description": "Whether hide the options that can not be selected", "value": {"type": "boolean", "kind": "expression"}}, {"name": "hourStep", "default": "1", "description": "Interval between hours in picker", "value": {"type": "number", "kind": "expression"}}, {"name": "inputReadOnly", "default": "false", "description": "Set the `readonly` attribute of the input tag (avoids virtual keyboard on touch devices)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "minuteStep", "default": "1", "description": "Interval between minutes in picker", "value": {"type": "number", "kind": "expression"}}, {"name": "open(v-model)", "default": "false", "description": "Whether to popup panel", "value": {"type": "boolean", "kind": "expression"}}, {"name": "placeholder", "default": "`Select a time`", "description": "Display when there's no value", "value": {"type": "string | [string, string]", "kind": "expression"}}, {"name": "placement", "default": "bottomLeft", "description": "The position where the selection box pops up", "value": {"type": "`bottomLeft` `bottomRight` `topLeft` `topRight`", "kind": "expression"}}, {"name": "popupClassName", "default": "-", "description": "The className of panel", "value": {"type": "string", "kind": "expression"}}, {"name": "popupStyle", "default": "-", "description": "The style of panel", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "renderExtraFooter", "default": "-", "description": "Called from time picker panel to render some addon to its bottom", "value": {"type": "v-slot:renderExtraFooter", "kind": "expression"}}, {"name": "secondStep", "default": "1", "description": "Interval between seconds in picker", "value": {"type": "number", "kind": "expression"}}, {"name": "showNow", "default": "-", "description": "Whether to show `Now` button on panel", "value": {"type": "boolean", "kind": "expression"}}, {"name": "suffixIcon", "default": "-", "description": "The custom suffix icon", "value": {"type": "v-slot:suffixIcon", "kind": "expression"}}, {"name": "use12Hours", "default": "false", "description": "Display as 12 hours format, with default format `h:mm:ss a`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "value(v-model)", "default": "-", "description": "To set time", "value": {"type": "[dayjs](https://day.js.org/)", "kind": "expression"}}, {"name": "valueFormat", "default": "-", "description": "optional, format of binding value. If not specified, the binding value will be a Date object", "value": {"type": "string, [date formats](https://day.js.org/docs/en/display/format)", "kind": "expression"}}]}, {"name": "a-timeline", "slots": [{"name": "pending", "description": "Set the last ghost node's existence or its content"}, {"name": "pendingDot", "description": "Set the dot of the last ghost node when pending is true"}], "events": [], "attributes": [{"name": "mode", "default": "`left`", "description": "By sending `alternate` the timeline will distribute the nodes to the left and right.", "value": {"type": "`left` | `alternate` | `right`", "kind": "expression"}}, {"name": "pending", "default": "`false`", "description": "Set the last ghost node's existence or its content", "value": {"type": "boolean|string|slot", "kind": "expression"}}, {"name": "pendingDot", "default": "`<LoadingOutlined />`", "description": "Set the dot of the last ghost node when pending is true", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "reverse", "default": "false", "description": "reverse nodes or not", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-timeline-item", "slots": [{"name": "dot", "description": "Customize timeline dot"}, {"name": "label", "description": "Set the label"}], "events": [], "attributes": [{"name": "color", "default": "`blue`", "description": "Set the circle's color to `blue`, `red`, `green` or other custom colors", "value": {"type": "string", "kind": "expression"}}, {"name": "dot", "default": "-", "description": "Customize timeline dot", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "label", "default": "-", "description": "Set the label", "value": {"type": "string | slot", "kind": "expression"}}, {"name": "position", "default": "-", "description": "Customize node position", "value": {"type": "`left` | `right`", "kind": "expression"}}]}, {"name": "a-tooltip", "slots": [{"name": "title", "description": "The text shown in the tooltip"}], "events": [{"name": "openChange", "description": "Callback executed when visibility of the tooltip card is changed"}], "attributes": [{"name": "title", "default": "-", "description": "The text shown in the tooltip", "value": {"type": "string|slot", "kind": "expression"}}, {"name": "align", "default": "-", "description": "this value will be merged into placement's config, please refer to the settings [dom-align](https://github.com/yiminghe/dom-align)", "value": {"type": "Object", "kind": "expression"}}, {"name": "arrowPointAtCenter", "default": "`false`", "description": "Whether the arrow is pointed at the center of target", "value": {"type": "boolean", "kind": "expression"}}, {"name": "arrow", "default": "`true`", "description": "Change arrow's visible state and change whether the arrow is pointed at the center of target.", "value": {"type": "boolean | { pointAtCenter: boolean}", "kind": "expression"}}, {"name": "autoAdjustOverflow", "default": "`true`", "description": "Whether to adjust popup placement automatically when popup is off screen", "value": {"type": "boolean", "kind": "expression"}}, {"name": "color", "default": "-", "description": "The background color", "value": {"type": "string", "kind": "expression"}}, {"name": "destroyTooltipOnHide", "default": "false", "description": "Whether to destroy tooltip on hide", "value": {"type": "boolean", "kind": "expression"}}, {"name": "getPopupContainer", "default": "() => document.body", "description": "The DOM container of the tip, the default behavior is to create a `div` element in `body`.", "value": {"type": "(triggerNode: HTMLElement) => HTMLElement", "kind": "expression"}}, {"name": "mouseEnterDelay", "default": "0.1", "description": "Delay in seconds, before tooltip is shown on mouse enter", "value": {"type": "number", "kind": "expression"}}, {"name": "mouseLeaveDelay", "default": "0.1", "description": "Delay in seconds, before tooltip is hidden on mouse leave", "value": {"type": "number", "kind": "expression"}}, {"name": "overlayClassName", "default": "-", "description": "Class name of the tooltip card", "value": {"type": "string", "kind": "expression"}}, {"name": "overlayStyle", "default": "-", "description": "Style of the tooltip card", "value": {"type": "object", "kind": "expression"}}, {"name": "overlayInnerStyle", "default": "-", "description": "Style of the tooltip inner content", "value": {"type": "object", "kind": "expression"}}, {"name": "placement", "default": "`top`", "description": "The position of the tooltip relative to the target, which can be one of `top` `left` `right` `bottom` `topLeft` `topRight` `bottomLeft` `bottomRight` `leftTop` `leftBottom` `rightTop` `rightBottom`", "value": {"type": "string", "kind": "expression"}}, {"name": "trigger", "default": "`hover`", "description": "Tooltip trigger mode", "value": {"type": "`hover` | `focus` | `click` | `contextmenu`", "kind": "expression"}}, {"name": "open(v-model)", "default": "`false`", "description": "Whether the floating tooltip card is open or not, Use `visible` under 4.0.0", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-tour", "slots": [{"name": "<PERSON><PERSON><PERSON>", "description": "custom indicator"}], "events": [{"name": "close", "description": "Callback function on shutdown"}, {"name": "finish", "description": "Callback function on finished"}, {"name": "change", "description": "Callback when the step changes. Current is the previous step"}], "attributes": [{"name": "arrow", "default": "`true`", "description": "Whether to show the arrow, including the configuration whether to point to the center of the element", "value": {"type": "`boolean`|`{ pointAtCenter: boolean}`", "kind": "expression"}}, {"name": "placement", "default": "`bottom`", "description": "Position of the guide card relative to the target element", "value": {"type": "`left` `leftTop` `leftBottom` `right` `rightTop` `rightBottom` `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight`", "kind": "expression"}}, {"name": "mask", "default": "`true`", "description": "Whether to enable masking, change mask style and fill color by pass custom props", "value": {"type": "`boolean` | `{ style?: CSSProperties; color?: string; }`", "kind": "expression"}}, {"name": "type", "default": "`default`", "description": "Type, affects the background color and text color", "value": {"type": "`default` `primary`", "kind": "expression"}}, {"name": "open", "default": "-", "description": "Open tour", "value": {"type": "`boolean`", "kind": "expression"}}, {"name": "current(v-model)", "default": "-", "description": "What is the current step", "value": {"type": "`number`", "kind": "expression"}}, {"name": "scrollIntoViewOptions", "default": "`true`", "description": "support pass custom scrollIntoView options", "value": {"type": "`boolean` | `ScrollIntoViewOptions`", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON>", "default": "-", "description": "custom indicator", "value": {"type": "`v-slot:indicatorsRender=\"{current, total}\"`", "kind": "expression"}}, {"name": "zIndex", "default": "`1001`", "description": "Tour's zIndex", "value": {"type": "`number`", "kind": "expression"}}]}, {"name": "a-tour-step", "slots": [], "events": [{"name": "close", "description": "Callback function on shutdown"}], "attributes": [{"name": "target", "default": "-", "description": "Get the element the guide card points to. Empty makes it show in center of screen", "value": {"type": "`() => HTMLElement` `HTMLElement`", "kind": "expression"}}, {"name": "arrow", "default": "`true`", "description": "Whether to show the arrow, including the configuration whether to point to the center of the element", "value": {"type": "`boolean` `{ pointAtCenter: boolean}`", "kind": "expression"}}, {"name": "cover", "default": "-", "description": "Displayed pictures or videos", "value": {"type": "`VueNode`", "kind": "expression"}}, {"name": "title", "default": "-", "description": "title", "value": {"type": "`VueNode`", "kind": "expression"}}, {"name": "description", "default": "-", "description": "description", "value": {"type": "`VueNode`", "kind": "expression"}}, {"name": "placement", "default": "`bottom`", "description": "Position of the guide card relative to the target element", "value": {"type": "`left` `leftTop` `leftBottom` `right` `rightTop` `rightBottom` `top` `topLeft` `topRight` `bottom` `bottomLeft` `bottomRight`", "kind": "expression"}}, {"name": "mask", "default": "`true`", "description": "Whether to enable masking, change mask style and fill color by pass custom props, the default follows the `mask` property of Tour", "value": {"type": "`boolean` | `{ style?: CSSProperties; color?: string; }`", "kind": "expression"}}, {"name": "type", "default": "`default`", "description": "Type, affects the background color and text color", "value": {"type": "`default` `primary`", "kind": "expression"}}, {"name": "nextButtonProps", "default": "-", "description": "Properties of the Next button", "value": {"type": "`{ children: <PERSON><PERSON><PERSON><PERSON>; onClick: Function }`", "kind": "expression"}}, {"name": "prevButtonProps", "default": "-", "description": "Properties of the previous button", "value": {"type": "`{ children: <PERSON><PERSON><PERSON><PERSON>; onClick: Function }`", "kind": "expression"}}, {"name": "scrollIntoViewOptions", "default": "`true`", "description": "support pass custom scrollIntoView options, the default follows the `scrollIntoViewOptions` property of Tour", "value": {"type": "`boolean` | `ScrollIntoViewOptions`", "kind": "expression"}}]}, {"name": "a-transfer", "slots": [{"name": "footer", "description": "customize the progress dot by setting a scoped slot"}, {"name": "render", "description": "The function to generate the item shown on a column. Based on an record (element of the dataSource array), this function should return a element which is generated from that record. Also, it can return a plain object with `value` and `label`, `label` is a element and `value` is for title"}], "events": [{"name": "change", "description": "A callback function that is executed when the transfer between columns is complete."}, {"name": "scroll", "description": "A callback function which is executed when scroll options list"}, {"name": "search", "description": "A callback function which is executed when search field are changed"}, {"name": "selectChange", "description": "A callback function which is executed when selected items are changed."}], "attributes": [{"name": "dataSource", "default": "\\[]", "description": "Used for setting the source data. The elements that are part of this array will be present the left column. Except the elements whose keys are included in `targetKeys` prop.", "value": {"type": "[{key: string.isRequired,title: string.isRequired,description: string,disabled: bool}]", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Whether disabled transfer", "value": {"type": "boolean", "kind": "expression"}}, {"name": "filterOption", "default": "", "description": "A function to determine whether an item should show in search result list", "value": {"type": "(inputValue, option): boolean", "kind": "expression"}}, {"name": "footer", "default": "", "description": "customize the progress dot by setting a scoped slot", "value": {"type": "slot=\"footer\" slot-scope=\"props\"", "kind": "expression"}}, {"name": "listStyle", "default": "", "description": "A custom CSS style used for rendering the transfer columns.", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "locale", "default": "`{ itemUnit: 'item', itemsUnit: 'items', notFoundContent: 'The list is empty', searchPlaceholder: 'Search here' }`", "description": "i18n text including filter, empty text, item unit, etc", "value": {"type": "object", "kind": "expression"}}, {"name": "oneWay", "default": "false", "description": "Display as single direction style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "operations", "default": "\\['>', '&lt;']", "description": "A set of operations that are sorted from top to bottom.", "value": {"type": "string[]", "kind": "expression"}}, {"name": "operationStyle", "default": "-", "description": "A custom CSS style used for rendering the operations column", "value": {"type": "CSSProperties", "kind": "expression"}}, {"name": "pagination", "default": "false", "description": "Use pagination. Not work in render props", "value": {"type": "boolean | { pageSize: number, simple: boolean, showSizeChanger?: boolean, showLessItems?: boolean }", "kind": "expression"}}, {"name": "render", "default": "", "description": "The function to generate the item shown on a column. Based on an record (element of the dataSource array), this function should return a element which is generated from that record. Also, it can return a plain object with `value` and `label`, `label` is a element and `value` is for title", "value": {"type": "Function(record) | slot", "kind": "expression"}}, {"name": "selectAllLabels", "default": "-", "description": "A set of customized labels for select all checkboxes on the header", "value": {"type": "VueNode | ((info: { selectedCount: number; totalCount: number }) => VueNode);", "kind": "expression"}}, {"name": "selected<PERSON>eys(v-model)", "default": "\\[]", "description": "A set of keys of selected items.", "value": {"type": "string[]", "kind": "expression"}}, {"name": "showSearch", "default": "false", "description": "If included, a search box is shown on each column.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showSelectAll", "default": "true", "description": "Show select all checkbox on the header", "value": {"type": "boolean", "kind": "expression"}}, {"name": "status", "default": "-", "description": "Set validation status", "value": {"type": "'error' | 'warning'", "kind": "expression"}}, {"name": "targetKeys(v-model)", "default": "\\[]", "description": "A set of keys of elements that are listed on the right column.", "value": {"type": "string[]", "kind": "expression"}}, {"name": "titles", "default": "-", "description": "A set of titles that are sorted from left to right.", "value": {"type": "string[]", "kind": "expression"}}]}, {"name": "a-tree", "slots": [{"name": "switcherIcon", "description": "customize collapse/expand icon of tree node"}, {"name": "title", "description": "custom title"}], "events": [], "attributes": [{"name": "allowDrop", "default": "-", "description": "Whether to allow dropping on the node", "value": {"type": "({ dropNode, dropPosition }) => boolean", "kind": "expression"}}, {"name": "autoExpandParent", "default": "false", "description": "Whether to automatically expand a parent treeNode", "value": {"type": "boolean", "kind": "expression"}}, {"name": "blockNode", "default": "false", "description": "Whether treeNode fill remaining horizontal space", "value": {"type": "boolean", "kind": "expression"}}, {"name": "checkable", "default": "false", "description": "Adds a `Checkbox` before the treeNodes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "checkedKeys(v-model)", "default": "\\[]", "description": "(Controlled) Specifies the keys of the checked treeNodes (PS: When this specifies the key of a treeNode which is also a parent treeNode, all the children treeNodes of will be checked; and vice versa, when it specifies the key of a treeNode which is a child treeNode, its parent treeNode will also be checked. When `checkable` and `checkStrictly` is true, its object has `checked` and `halfChecked` property. Regardless of whether the child or parent treeNode is checked, they won't impact each other.", "value": {"type": "string[] | number[] | {checked: string\\[] | number\\[], halfChecked: string\\[] | number\\[]}", "kind": "expression"}}, {"name": "checkStrictly", "default": "false", "description": "Check treeNode precisely; parent treeN<PERSON> and children treeNodes are not associated", "value": {"type": "boolean", "kind": "expression"}}, {"name": "defaultExpandAll", "default": "false", "description": "Whether to expand all treeNodes by default", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "whether disabled the tree", "value": {"type": "bool", "kind": "expression"}}, {"name": "draggable", "default": "false", "description": "Specifies whether this Tree is draggable (IE > 8)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "expandedKeys(v-model)", "default": "\\[]", "description": "(Controlled) Specifies the keys of the expanded treeNodes", "value": {"type": "string[] | number[]", "kind": "expression"}}, {"name": "fieldNames", "default": "{ children:'children', title:'title', key:'key' }", "description": "Replace the title,key and children fields in treeNode with the corresponding fields in treeData", "value": {"type": "object", "kind": "expression"}}, {"name": "filterTreeNode", "default": "-", "description": "Defines a function to filter (highlight) treeNodes. When the function returns `true`, the corresponding treeNode will be highlighted", "value": {"type": "function(node)", "kind": "expression"}}, {"name": "height", "default": "-", "description": "Config virtual scroll height. Will not support horizontal scroll when enable this", "value": {"type": "number", "kind": "expression"}}, {"name": "loadData", "default": "-", "description": "Load data asynchronously", "value": {"type": "function(node)", "kind": "expression"}}, {"name": "loadedKeys", "default": "\\[]", "description": "(Controlled) Set loaded tree nodes. Need work with `loadData`", "value": {"type": "string[] | number[]", "kind": "expression"}}, {"name": "multiple", "default": "false", "description": "Allows selecting multiple treeNodes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "selectable", "default": "true", "description": "whether can be selected", "value": {"type": "boolean", "kind": "expression"}}, {"name": "selected<PERSON>eys(v-model)", "default": "-", "description": "(Controlled) Specifies the keys of the selected treeNodes", "value": {"type": "string[] | number[]", "kind": "expression"}}, {"name": "showIcon", "default": "false", "description": "Shows the icon before a TreeNode's title. There is no default style; you must set a custom style for it if set to `true`", "value": {"type": "boolean", "kind": "expression"}}, {"name": "showLine", "default": "false", "description": "Shows a connecting line", "value": {"type": "boolean | {showLeafIcon: boolean}(3.0+)", "kind": "expression"}}, {"name": "switcherIcon", "default": "-", "description": "customize collapse/expand icon of tree node", "value": {"type": "v-slot:switcherIcon=\"{active, checked, expanded, loading, selected, halfChecked, title, key, children, dataRef, data, defaultIcon, switcherCls}\"", "kind": "expression"}}, {"name": "title", "default": "", "description": "custom title", "value": {"type": "slot", "kind": "expression"}}, {"name": "treeData", "default": "-", "description": "treeNode of tree, please use `treeNodes` before v1.1.4", "value": {"type": "[TreeNode[]](#treenode)", "kind": "expression"}}, {"name": "virtual", "default": "true", "description": "Disable virtual scroll when set to false", "value": {"type": "boolean", "kind": "expression"}}, {"name": "scrollTo({ key: string | number; align?: 'top' | 'bottom' | 'auto'; offset?: number })", "description": "Scroll to key item in virtual scroll", "value": {"type": "", "kind": "expression"}}]}, {"name": "a-tree-node", "slots": [{"name": "icon", "description": "customize icon. When you pass component, whose render will receive full TreeNode props as component props"}], "events": [], "attributes": [{"name": "checkable", "default": "-", "description": "When Tree is checkable, set TreeNode display Checkbox or not", "value": {"type": "boolean", "kind": "expression"}}, {"name": "class", "default": "-", "description": "className", "value": {"type": "string", "kind": "expression"}}, {"name": "disableCheckbox", "default": "false", "description": "Disables the checkbox of the treeNode", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disables the treeNode", "value": {"type": "boolean", "kind": "expression"}}, {"name": "icon", "default": "-", "description": "customize icon. When you pass component, whose render will receive full TreeNode props as component props", "value": {"type": "slot|slot-scope", "kind": "expression"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "default": "-", "description": "Determines if this is a leaf node(effective when `loadData` is specified)", "value": {"type": "boolean", "kind": "expression"}}, {"name": "key", "default": "internal calculated position of treeNode", "description": "Used with (default)ExpandedKeys / (default)CheckedKeys / (default)SelectedKeys. P.S.: It must be unique in all of treeNodes of the tree!", "value": {"type": "string | number", "kind": "expression"}}, {"name": "selectable", "default": "true", "description": "Set whether the treeNode can be selected", "value": {"type": "boolean", "kind": "expression"}}, {"name": "style", "default": "-", "description": "style", "value": {"type": "string|object", "kind": "expression"}}, {"name": "title", "default": "'---'", "description": "Title", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-directory-tree", "slots": [], "events": [], "attributes": [{"name": "expandAction", "default": "click", "description": "Directory open logic, optional `false` `'click'` `'dblclick'`", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-tree-select", "slots": [], "events": [], "attributes": []}, {"name": "a-typography", "slots": [], "events": [], "attributes": []}, {"name": "a-typography-text", "slots": [], "events": [], "attributes": [{"name": "code", "default": "false", "description": "Code style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "content(v-model)", "default": "-", "description": "When using ellipsis or editable, use content instead of children", "value": {"type": "string", "kind": "expression"}}, {"name": "copyable", "default": "false", "description": "Whether to be copyable, customize it via setting an object", "value": {"type": "boolean | [copyable](#copyable)", "kind": "expression"}}, {"name": "delete", "default": "false", "description": "Deleted line style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disabled content", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "default": "false", "description": "If editable. Can control edit state when is object", "value": {"type": "boolean | [editable](#editable)", "kind": "expression"}}, {"name": "ellipsis", "default": "false", "description": "Display ellipsis when text overflows", "value": {"type": "boolean", "kind": "expression"}}, {"name": "keyboard", "default": "false", "description": "Keyboard style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "mark", "default": "false", "description": "Marked style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "strong", "default": "false", "description": "Bold style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "type", "default": "-", "description": "Content type", "value": {"type": "`secondary` | `success` | `warning` | `danger`", "kind": "expression"}}, {"name": "underline", "default": "false", "description": "Underlined style", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-typography-title", "slots": [], "events": [], "attributes": [{"name": "code", "default": "false", "description": "Code style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "content(v-model)", "default": "-", "description": "When using ellipsis or editable, use content instead of children", "value": {"type": "string", "kind": "expression"}}, {"name": "copyable", "default": "false", "description": "Whether to be copyable, customize it via setting an object", "value": {"type": "boolean | [copyable](#copyable)", "kind": "expression"}}, {"name": "delete", "default": "false", "description": "Deleted line style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disabled content", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "default": "false", "description": "If editable. Can control edit state when is object", "value": {"type": "boolean | [editable](#editable)", "kind": "expression"}}, {"name": "ellipsis", "default": "false", "description": "Display ellipsis when text overflows, can configure rows and expandable by using object", "value": {"type": "boolean | [ellipsis](#ellipsis)", "kind": "expression"}}, {"name": "level", "default": "1", "description": "Set content importance. Match with `h1`, `h2`, `h3`, `h4`, `h5`", "value": {"type": "number: 1, 2, 3, 4, 5", "kind": "expression"}}, {"name": "mark", "default": "false", "description": "Marked style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "type", "default": "-", "description": "Content type", "value": {"type": "`secondary` | `success` | `warning` | `danger`", "kind": "expression"}}, {"name": "underline", "default": "false", "description": "Underlined style", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-typography-paragraph", "slots": [], "events": [], "attributes": [{"name": "code", "default": "false", "description": "Code style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "content(v-model)", "default": "-", "description": "When using ellipsis or editable, use content instead of children", "value": {"type": "string", "kind": "expression"}}, {"name": "copyable", "default": "false", "description": "Whether to be copyable, customize it via setting an object", "value": {"type": "boolean | [copyable](#copyable)", "kind": "expression"}}, {"name": "delete", "default": "false", "description": "Deleted line style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "false", "description": "Disabled content", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "default": "false", "description": "If editable. Can control edit state when is object", "value": {"type": "boolean | [editable](#editable)", "kind": "expression"}}, {"name": "ellipsis", "default": "false", "description": "Display ellipsis when text overflows, can configure rows and expandable by using object", "value": {"type": "boolean | [ellipsis](#ellipsis)", "kind": "expression"}}, {"name": "mark", "default": "false", "description": "Marked style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "strong", "default": "false", "description": "Bold style", "value": {"type": "boolean", "kind": "expression"}}, {"name": "type", "default": "-", "description": "Content type", "value": {"type": "`secondary` | `success` | `warning` | `danger`", "kind": "expression"}}, {"name": "underline", "default": "false", "description": "Underlined style", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-upload", "slots": [{"name": "downloadIcon", "description": "custom download icon"}, {"name": "iconRender", "description": "Custom show icon"}, {"name": "itemRender", "description": "Custom item of uploadList"}, {"name": "previewIcon", "description": "custom preview icon"}, {"name": "removeIcon", "description": "custom remove icon"}], "events": [{"name": "change", "description": "A callback function, can be executed when uploading state is changing. See [change](#change)"}, {"name": "download", "description": "Click the method to download the file, pass the method to perform the method logic, do not pass the default jump to the new TAB."}, {"name": "drop", "description": "A callback function executed when files are dragged and dropped into upload area"}, {"name": "preview", "description": "A callback function, will be executed when file link or preview icon is clicked."}, {"name": "reject", "description": "A callback function, will be executed when drop files is not accept."}, {"name": "remove", "description": "A callback function, will be executed when removing file button is clicked, remove event will be prevented when return value is false or a Promise which resolve(false) or reject"}], "attributes": [{"name": "accept", "default": "-", "description": "File types that can be accepted. See [input accept Attribute](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#accept)", "value": {"type": "string", "kind": "expression"}}, {"name": "action", "default": "-", "description": "Uploading URL", "value": {"type": "string|(file) => `Promise`", "kind": "expression"}}, {"name": "beforeUpload", "default": "-", "description": "Hook function which will be executed before uploading. Uploading will be stopped with `false` or a rejected Promise returned.", "value": {"type": "(file, fileList) => `boolean` | `Promise`", "kind": "expression"}}, {"name": "customRequest", "default": "-", "description": "override for the default xhr behavior allowing for additional customization and ability to implement your own XMLHttpRequest", "value": {"type": "function", "kind": "expression"}}, {"name": "data", "default": "-", "description": "Uploading params or function which can return uploading params.", "value": {"type": "object|function(file)", "kind": "expression"}}, {"name": "directory", "default": "false", "description": "Support upload whole directory（[caniuse](https://caniuse.com/#feat=input-file-directory)）", "value": {"type": "boolean", "kind": "expression"}}, {"name": "disabled", "default": "-", "description": "disable upload button", "value": {"type": "boolean", "kind": "expression"}}, {"name": "downloadIcon", "default": "-", "description": "custom download icon", "value": {"type": "v-slot:iconRender=\"{file: UploadFile}\"", "kind": "expression"}}, {"name": "fileList", "default": "-", "description": "List of files that have been uploaded (controlled). Here is a common issue [#2423](https://github.com/ant-design/ant-design/issues/2423) when using it", "value": {"type": "object[]", "kind": "expression"}}, {"name": "headers", "default": "-", "description": "Set request headers, valid above IE10.", "value": {"type": "object", "kind": "expression"}}, {"name": "iconRender", "default": "-", "description": "Custom show icon", "value": {"type": "v-slot:iconRender=\"{file: UploadFile, listType?: UploadListType}\"", "kind": "expression"}}, {"name": "isImageUrl", "default": "-", "description": "Customize if render &lt;img /> in thumbnail", "value": {"type": "(file: UploadFile) => boolean", "kind": "expression"}}, {"name": "itemRender", "default": "-", "description": "Custom item of uploadList", "value": {"type": "v-slot:itemRender=\"{originNode: VNode, file: UploadFile, fileList: object[], actions: { download: function, preview: function, remove: function }\"", "kind": "expression"}}, {"name": "listType", "default": "`text`", "description": "Built-in stylesheets, support for three types: `text`, `picture` or `picture-card`", "value": {"type": "string", "kind": "expression"}}, {"name": "maxCount", "default": "-", "description": "Limit the number of uploaded files. Will replace current one when `maxCount` is `1`", "value": {"type": "number", "kind": "expression"}}, {"name": "method", "default": "`post`", "description": "http method of upload request", "value": {"type": "string", "kind": "expression"}}, {"name": "multiple", "default": "false", "description": "Whether to support selected multiple file. `IE10+` supported. You can select multiple files with CTRL holding down while multiple is set to be true", "value": {"type": "boolean", "kind": "expression"}}, {"name": "name", "default": "`file`", "description": "The name of uploading file", "value": {"type": "string", "kind": "expression"}}, {"name": "openFileDialogOnClick", "default": "true", "description": "Click open file dialog", "value": {"type": "boolean", "kind": "expression"}}, {"name": "previewFile", "default": "-", "description": "Customize preview file logic", "value": {"type": "(file: File | Blob) => Promise&lt;dataURL: string>", "kind": "expression"}}, {"name": "previewIcon", "default": "-", "description": "custom preview icon", "value": {"type": "v-slot:iconRender=\"{file: UploadFile}\"", "kind": "expression"}}, {"name": "progress", "default": "{ strokeWidth: 2, showInfo: false }", "description": "Custom progress bar", "value": {"type": "[ProgressProps](/components/progress/#api) (support `type=\"line\"` only)", "kind": "expression"}}, {"name": "removeIcon", "default": "-", "description": "custom remove icon", "value": {"type": "v-slot:iconRender=\"{file: UploadFile}\"", "kind": "expression"}}, {"name": "showUploadList", "default": "true", "description": "Whether to show default upload list, could be an object to specify `showPreviewIcon`, `showRemoveIcon` and `showDownloadIcon` individually", "value": {"type": "boolean | { showPreviewIcon?: boolean, showRemoveIcon?: boolean, showDownloadIcon?: boolean }", "kind": "expression"}}, {"name": "supportServerRender", "default": "false", "description": "Need to be turned on while the server side is rendering.", "value": {"type": "boolean", "kind": "expression"}}, {"name": "withCredentials", "default": "false", "description": "ajax upload with cookie sent", "value": {"type": "boolean", "kind": "expression"}}]}, {"name": "a-upload-file", "slots": [], "events": [], "attributes": [{"name": "crossOrigin", "default": "-", "description": "CORS settings attributes", "value": {"type": "`'anonymous'` | `'use-credentials'` | `''`", "kind": "expression"}}, {"name": "name", "default": "-", "description": "File name", "value": {"type": "string", "kind": "expression"}}, {"name": "percent", "default": "-", "description": "Upload progress percent", "value": {"type": "number", "kind": "expression"}}, {"name": "status", "default": "-", "description": "Upload status. Show different style when configured", "value": {"type": "`error` | `success` | `done` | `uploading` | `removed`", "kind": "expression"}}, {"name": "thumbUrl", "default": "-", "description": "Thumb image url", "value": {"type": "string", "kind": "expression"}}, {"name": "uid", "default": "-", "description": "unique id. Will auto generate when not provided", "value": {"type": "string", "kind": "expression"}}, {"name": "url", "default": "-", "description": "Download url", "value": {"type": "string", "kind": "expression"}}]}, {"name": "a-watermark", "slots": [], "events": [], "attributes": [{"name": "width", "default": "120", "description": "The width of the watermark, the default value of `content` is its own width", "value": {"type": "number", "kind": "expression"}}, {"name": "height", "default": "64", "description": "The height of the watermark, the default value of `content` is its own height", "value": {"type": "number", "kind": "expression"}}, {"name": "rotate", "default": "-22", "description": "When the watermark is drawn, the rotation Angle, unit `°`", "value": {"type": "number", "kind": "expression"}}, {"name": "zIndex", "default": "9", "description": "The z-index of the appended watermark element", "value": {"type": "number", "kind": "expression"}}, {"name": "image", "default": "-", "description": "Image source, it is recommended to export 2x or 3x image, high priority", "value": {"type": "string", "kind": "expression"}}, {"name": "content", "default": "-", "description": "Watermark text content", "value": {"type": "string | string[]", "kind": "expression"}}, {"name": "font", "default": "[Font](#font)", "description": "Text style", "value": {"type": "[Font](#font)", "kind": "expression"}}, {"name": "gap", "default": "\\[100, 100\\]", "description": "The spacing between watermarks", "value": {"type": "[number, number]", "kind": "expression"}}, {"name": "offset", "default": "\\[gap\\[0\\]/2, gap\\[1\\]/2\\]", "description": "The offset of the watermark from the upper left corner of the container. The default is `gap/2`", "value": {"type": "[number, number]", "kind": "expression"}}]}, {"name": "a-anchor-link", "slots": [], "events": [], "attributes": []}, {"name": "a-auto-complete-opt-group", "slots": [], "events": [], "attributes": []}, {"name": "a-auto-complete-option", "slots": [], "events": [], "attributes": []}, {"name": "a-badge", "slots": [], "events": [], "attributes": []}, {"name": "a-badge-ribbon", "slots": [], "events": [], "attributes": []}, {"name": "a-button-group", "slots": [], "events": [], "attributes": []}, {"name": "a-card-grid", "slots": [], "events": [], "attributes": []}, {"name": "a-checkable-tag", "slots": [], "events": [], "attributes": []}, {"name": "a-checkbox-group", "slots": [], "events": [], "attributes": []}, {"name": "a-col", "slots": [], "events": [], "attributes": []}, {"name": "a-style-provider", "slots": [], "events": [], "attributes": []}, {"name": "a-descriptions-item", "slots": [], "events": [], "attributes": []}, {"name": "a-form-item-rest", "slots": [], "events": [], "attributes": []}, {"name": "a-image-preview-group", "slots": [], "events": [], "attributes": []}, {"name": "a-layout-content", "slots": [], "events": [], "attributes": []}, {"name": "a-layout-footer", "slots": [], "events": [], "attributes": []}, {"name": "a-layout-header", "slots": [], "events": [], "attributes": []}, {"name": "a-locale-provider", "slots": [], "events": [], "attributes": []}, {"name": "a-mentions-option", "slots": [], "events": [], "attributes": []}, {"name": "a-month-picker", "slots": [], "events": [], "attributes": []}, {"name": "a-quarter-picker", "slots": [], "events": [], "attributes": []}, {"name": "a-radio-button", "slots": [], "events": [], "attributes": []}, {"name": "a-range-picker", "slots": [], "events": [], "attributes": []}, {"name": "a-row", "slots": [], "events": [], "attributes": []}, {"name": "a-select-opt-group", "slots": [], "events": [], "attributes": []}, {"name": "a-select-option", "slots": [], "events": [], "attributes": []}, {"name": "a-skeleton-avatar", "slots": [], "events": [], "attributes": []}, {"name": "a-skeleton-button", "slots": [], "events": [], "attributes": []}, {"name": "a-skeleton-image", "slots": [], "events": [], "attributes": []}, {"name": "a-skeleton-input", "slots": [], "events": [], "attributes": []}, {"name": "a-step", "slots": [], "events": [], "attributes": []}, {"name": "a-sub-menu", "slots": [], "events": [], "attributes": []}, {"name": "a-tab-pane", "slots": [], "events": [], "attributes": []}, {"name": "a-table-column", "slots": [], "events": [], "attributes": []}, {"name": "a-table-column-group", "slots": [], "events": [], "attributes": []}, {"name": "a-table-summary", "slots": [], "events": [], "attributes": []}, {"name": "a-table-summary-cell", "slots": [], "events": [], "attributes": []}, {"name": "a-table-summary-row", "slots": [], "events": [], "attributes": []}, {"name": "a-textarea", "slots": [], "events": [], "attributes": []}, {"name": "a-time-range-picker", "slots": [], "events": [], "attributes": []}, {"name": "a-tree-select-node", "slots": [], "events": [], "attributes": []}, {"name": "a-typography-link", "slots": [], "events": [], "attributes": []}, {"name": "a-upload-dragger", "slots": [], "events": [], "attributes": []}, {"name": "a-week-picker", "slots": [], "events": [], "attributes": []}, {"name": "a-qr-code", "slots": [], "events": [], "attributes": []}, {"name": "a-float-button-group", "slots": [], "events": [], "attributes": []}, {"name": "a-back-top", "slots": [], "events": [], "attributes": []}], "attributes": [], "types-syntax": "typescript"}}}