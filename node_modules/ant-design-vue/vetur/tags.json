{"a-affix": {"attributes": ["offsetBottom", "offsetTop", "target"]}, "a-alert": {"attributes": ["action", "afterClose", "banner", "closable", "closeIcon", "closeText", "description", "icon", "message", "showIcon", "type"]}, "a-anchor": {"attributes": ["affix", "bounds", "getContainer", "getCurrentAnchor", "offsetBottom", "offsetTop", "showInkInFixed", "targetOffset", "wrapperClass", "wrapperStyle", "items", "direction", "customTitle"]}, "a-anchor-item": {"attributes": ["key", "href", "target", "title", "children"]}, "a-app": {"attributes": ["message", "notification"]}, "a-auto-complete": {"attributes": ["allowClear", "autofocus", "backfill", "bordered", "clearIcon", "default (for customize input element)", "defaultActiveFirstOption", "defaultOpen", "disabled", "popupClassName", "dropdownMatchSelectWidth", "dropdownMenuStyle", "filterOption", "open", "option", "options", "placeholder", "status", "v-model:value"]}, "a-avatar": {"attributes": ["alt", "crossOrigin", "draggable", "gap", "icon", "loadError", "shape", "size", "src", "srcset"]}, "a-avatar-group": {"attributes": ["maxCount", "maxPopoverPlacement", "maxPopoverTrigger", "maxStyle", "size", "shape"]}, "a-breadcrumb": {"attributes": ["itemRender", "params", "routes", "separator"]}, "a-breadcrumb-item": {"attributes": ["href", "overlay"]}, "a-breadcrumb-separator": {"attributes": []}, "a-button": {"attributes": ["block", "danger", "disabled", "ghost", "href", "htmlType", "icon", "loading", "shape", "size", "target", "type"]}, "a-calendar": {"attributes": ["date<PERSON>ell<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabledDate", "fullscreen", "headerRender", "locale", "mode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validRange", "value(v-model)", "valueFormat"]}, "a-card": {"attributes": ["activeTabKey", "bodyStyle", "bordered", "defaultActiveTabKey", "extra", "headStyle", "hoverable", "loading", "size", "tabList", "title", "type", "actions", "cover", "customTab", "extra", "tabBarExtraContent", "title"]}, "a-card-meta": {"attributes": ["avatar", "description", "title"]}, "a-carousel": {"attributes": ["autoplay", "dotPosition", "dots", "dotsClass", "easing", "effect", "afterChange", "beforeChange"]}, "a-cascader": {"attributes": ["allowClear", "autofocus", "bordered", "clearIcon", "changeOnSelect", "disabled", "displayRender", "popupClassName", "dropdownStyle", "expandIcon", "expandTrigger", "fieldNames", "getPopupContainer", "loadData", "maxTag<PERSON>ount", "maxTagPlaceholder", "multiple", "notFoundContent", "open", "options", "placeholder", "placement", "removeIcon", "searchValue", "showSearch", "size", "status", "suffixIcon", "showCheckedStrategy", "tagRender", "value(v-model)"]}, "a-checkbox": {"attributes": ["autofocus", "checked(v-model)", "disabled", "indeterminate", "value", "disabled", "name", "options", "value(v-model)", "blur()", "focus()"]}, "a-collapse": {"attributes": ["accordion", "<PERSON><PERSON><PERSON>(v-model)", "bordered", "collapsible", "destroyInactivePanel", "expandIcon", "expandIconPosition", "ghost"]}, "a-collapse-panel": {"attributes": ["collapsible", "disabled", "extra", "forceRender", "header", "key", "showArrow"]}, "a-comment": {"attributes": ["actions", "author", "avatar", "content", "datetime"]}, "a-config-provider": {"attributes": ["autoInsertSpaceInButton", "componentSize", "csp", "direction", "dropdownMatchSelectWidth", "form", "getPopupContainer", "getTargetContainer", "input", "locale", "pageHeader", "prefixCls", "renderEmpty", "space", "transformCellText", "virtual", "wave"]}, "a-date-picker": {"attributes": ["allowClear", "autofocus", "bordered", "dateRender", "disabled", "disabledDate", "format", "dropdownClassName", "getPopupContainer", "inputReadOnly", "locale", "mode", "nextIcon", "open", "picker", "placeholder", "placement", "popupStyle", "presets", "prevIcon", "size", "status", "suffixIcon", "superNextIcon", "superPrevIcon", "valueFormat"]}, "a-descriptions": {"attributes": ["bordered", "colon", "column", "contentStyle", "extra", "labelStyle", "layout", "size", "title"]}, "a-divider": {"attributes": ["dashed", "orientation", "<PERSON><PERSON><PERSON><PERSON>", "plain", "type"]}, "a-drawer": {"attributes": ["autofocus", "bodyStyle", "class", "closable", "closeIcon", "contentWrapperStyle", "destroyOnClose", "extra", "footer", "footerStyle", "forceRender", "getContainer", "headerStyle", "height", "keyboard", "mask", "maskClosable", "maskStyle", "placement", "push", "rootClassName", "rootStyle", "style", "size", "style", "title", "open(v-model)", "width", "zIndex"]}, "a-empty": {"attributes": ["description", "image", "imageStyle"]}, "a-dropdown": {"attributes": ["align", "arrow", "destroyPopupOnHide", "disabled", "getPopupContainer", "overlay(v-slot)", "overlayClassName", "overlayStyle", "placement", "trigger", "open(v-model)"]}, "a-dropdown-button": {"attributes": ["disabled", "icon", "loading", "overlay(v-slot)", "placement", "size", "trigger", "type", "open(v-model)"]}, "a-flex": {"attributes": ["vertical", "wrap", "justify", "align", "flex", "gap", "component"]}, "a-float-button": {"attributes": ["icon", "description", "tooltip", "type", "shape", "href", "target", "badge"]}, "a-form": {"attributes": ["colon", "disabled", "hideRequiredMark", "labelAlign", "labelCol", "labelWrap", "layout", "model", "name", "noStyle", "rules", "scrollToFirstError", "validateOnRuleChange", "validate<PERSON><PERSON>ger", "wrapperCol"]}, "a-form-item": {"attributes": ["autoLink", "colon", "extra", "hasFeedback", "help", "htmlFor", "label", "labelAlign", "labelCol", "name", "required", "rules", "tooltip", "validate<PERSON><PERSON><PERSON>", "validateStatus", "validate<PERSON><PERSON>ger", "wrapperCol"]}, "a-grid": {"attributes": []}, "a-icon": {"attributes": []}, "a-common": {"attributes": ["rotate", "spin", "style", "twoToneColor"]}, "a-custom": {"attributes": ["component", "rotate", "spin", "style", "extraCommonProps", "scriptUrl", "class", "fill", "height", "style", "width"]}, "a-image": {"attributes": ["alt", "fallback", "height", "placeholder", "preview", "src", "previewMask", "width"]}, "a-input": {"attributes": ["addonAfter", "addonBefore", "allowClear", "bordered", "clearIcon", "defaultValue", "disabled", "id", "maxlength", "prefix", "showCount", "status", "size", "suffix", "type", "value(v-model)", "change", "pressEnter"]}, "a-input-search": {"attributes": ["enterButton", "loading", "search"]}, "a-input-group": {"attributes": ["compact", "size"]}, "a-input-password": {"attributes": ["visible(v-model)", "iconRender", "visibilityToggle"]}, "a-input-number": {"attributes": ["addonAfter", "addonBefore", "autofocus", "bordered", "controls", "decimalSeparator", "defaultValue", "disabled", "formatter", "keyboard", "max", "min", "parser", "precision", "prefix", "size", "status", "step", "stringMode", "value(v-model)", "upIcon", "downIcon"]}, "a-layout": {"attributes": ["class", "hasSider", "style"]}, "a-layout-sider": {"attributes": ["breakpoint", "class", "collapsed(v-model)", "collapsedWidth", "collapsible", "defaultCollapsed", "reverseArrow", "style", "theme", "trigger", "width", "zeroWidthTriggerStyle"]}, "a-list": {"attributes": ["bordered", "dataSource", "footer", "grid", "header", "itemLayout", "loading", "loadMore", "locale", "pagination", "renderItem", "<PERSON><PERSON><PERSON>", "split", "column", "gutter", "size", "xxxl", "xs", "sm", "md", "lg", "xl", "xxl"]}, "a-list-item": {"attributes": ["actions", "extra"]}, "a-list-item-meta": {"attributes": ["avatar", "description", "title"]}, "a-mentions": {"attributes": []}, "a-menu": {"attributes": ["forceSubMenuRender", "inlineCollapsed", "inlineIndent", "items", "mode", "multiple", "openKeys(v-model)", "overflowedIndicator", "selectable", "selected<PERSON>eys(v-model)", "style", "subMenuCloseDelay", "subMenuOpenDelay", "theme", "triggerSubMenuAction", "click", "deselect", "openChange", "select"]}, "a-menu-item": {"attributes": ["disabled", "key", "title"]}, "a-menu-item-type": {"attributes": ["danger", "disabled", "icon", "key", "label", "title"]}, "a-sub-menu-type": {"attributes": ["children", "disabled", "icon", "key", "label", "popupClassName", "popupOffset", "theme", "onTitleClick"]}, "a-menu-item-group-type": {"attributes": ["children", "label"]}, "a-menu-divider-type": {"attributes": ["dashed"]}, "a-menu-sub-menu": {"attributes": ["disabled", "expandIcon", "key", "popupClassName", "popupOffset", "title", "titleClick"]}, "a-menu-item-group": {"attributes": ["children", "title"]}, "a-menu-divider": {"attributes": ["dashed"]}, "a-message": {"attributes": ["content", "duration", "onClose"]}, "a-modal": {"attributes": ["afterClose", "bodyStyle", "cancelButtonProps", "cancelText", "centered", "closable", "closeIcon", "confirmLoading", "destroyOnClose", "footer", "forceRender", "getContainer", "mask", "maskClosable", "maskStyle", "okButtonProps", "okText", "okType", "title", "open(v-model)", "width", "wrapClassName", "zIndex"]}, "a-notification": {"attributes": ["bottom", "btn", "class", "closeIcon", "description", "duration", "getContainer", "icon", "key", "message", "placement", "style", "top", "onClick", "onClose"]}, "a-page-header": {"attributes": ["avatar", "backIcon", "breadcrumb", "extra", "footer", "ghost", "subTitle", "tags", "title"]}, "a-pagination": {"attributes": ["current(v-model)", "defaultCurrent", "defaultPageSize", "disabled", "hideOnSinglePage", "itemRender", "pageSize(v-model)", "pageSizeOptions", "responsive", "showLessItems", "showQuickJumper", "showSizeChanger", "showTitle", "showTotal", "simple", "size", "total"]}, "a-popconfirm": {"attributes": ["cancelButton", "cancelButtonProps", "cancelText", "disabled", "icon", "okButton", "okButtonProps", "okText", "okType", "showCancel", "title", "description", "visible (v-model)"]}, "a-popover": {"attributes": ["content", "title"]}, "a-progress": {"attributes": ["format", "percent", "showInfo", "status", "strokeColor", "strokeLinecap", "success", "title", "trailColor", "type", "size"]}, "a-qrcode": {"attributes": ["value", "type", "icon", "size", "iconSize", "color", "bgColor", "bordered", "errorLevel", "status"]}, "a-radio": {"attributes": ["blur()", "focus()"]}, "a-radio-radio-button": {"attributes": ["autofocus", "checked(v-model)", "disabled", "value"]}, "a-radio-group": {"attributes": ["buttonStyle", "disabled", "name", "options", "optionType", "size", "value(v-model)", "change"]}, "a-rate": {"attributes": ["allowClear", "allowHalf", "autofocus", "character", "count", "disabled", "tooltips", "value(v-model)"]}, "a-result": {"attributes": ["extra", "icon", "status", "subTitle", "title"]}, "a-segmented": {"attributes": ["block", "disabled", "options", "size", "value", "label"]}, "a-select": {"attributes": ["allowClear", "autoClearSearchValue", "autofocus", "bordered", "clearIcon", "defaultActiveFirstOption", "defaultOpen", "disabled", "popupClassName", "dropdownMatchSelectWidth", "dropdownMenuStyle", "dropdownRender", "dropdownStyle", "fieldNames", "filterOption", "filterSort", "firstActiveValue", "getPopupContainer", "labelInValue", "listHeight", "loading", "maxTag<PERSON>ount", "maxTagPlaceholder", "maxTagTextLength", "menuItemSelectedIcon", "mode", "notFoundContent", "open", "option", "optionFilterProp", "optionLabelProp", "options", "placeholder", "placement", "removeIcon", "searchValue", "showArrow", "showSearch", "size", "status", "suffixIcon", "tagRender", "tokenSeparators", "value(v-model)", "virtual", "blur()", "focus()"]}, "a-skeleton": {"attributes": ["active", "avatar", "loading", "paragraph", "title"]}, "a-skeleton-avatar-props": {"attributes": ["shape", "size"]}, "a-skeleton-title-props": {"attributes": ["width"]}, "a-skeleton-paragraph-props": {"attributes": ["rows", "width"]}, "a-skeleton-button-props": {"attributes": ["active", "block", "shape", "size"]}, "a-skeleton-input-props": {"attributes": ["active", "size"]}, "a-slider": {"attributes": ["autofocus", "disabled", "dots", "handleStyle", "included", "mark", "marks", "max", "min", "range", "reverse", "step", "trackStyle", "value(v-model)", "vertical", "tip<PERSON><PERSON><PERSON><PERSON>", "tooltipPlacement", "tooltipOpen", "getTooltipPopupContainer"]}, "a-space": {"attributes": ["align", "direction", "size", "split", "wrap"]}, "a-space-compact": {"attributes": ["block", "direction", "size"]}, "a-spin": {"attributes": ["delay", "indicator", "size", "spinning", "tip", "wrapperClassName"]}, "a-statistic": {"attributes": ["decimalSeparator", "formatter", "groupSeparator", "precision", "prefix", "suffix", "title", "value", "valueStyle"]}, "a-statistic-countdown": {"attributes": ["format", "prefix", "suffix", "title", "value", "valueStyle", "finish"]}, "a-steps": {"attributes": ["current(v-model)", "direction", "initial", "labelPlacement", "percent", "progressDot", "responsive", "size", "status", "type", "items", "change"]}, "a-steps-step": {"attributes": ["description", "disabled", "icon", "status", "subTitle", "title"]}, "a-switch": {"attributes": ["autofocus", "checked(v-model)", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "checkedValue", "disabled", "loading", "size", "unChecked<PERSON><PERSON><PERSON>n", "unCheckedValue"]}, "a-table": {"attributes": ["bodyCell", "bordered", "childrenColumnName", "columns", "components", "customFilterDropdown", "customFilterIcon", "customHeaderRow", "customRow", "dataSource", "defaultExpandAllRows", "defaultExpandedRowKeys", "emptyText", "expandedRowKeys(v-model)", "expandedRowRender", "expandFixed", "expandColumnTitle", "expandIcon", "expandRowByClick", "footer", "getPopupContainer", "headerCell", "indentSize", "loading", "locale", "pagination", "rowClassName", "rowExpandable", "<PERSON><PERSON><PERSON>", "rowSelection", "scroll", "showExpandColumn", "showHeader", "showSorterTooltip", "size", "sortDirections", "sticky", "summary", "tableLayout", "title", "transformCellText"]}, "a-tabs": {"attributes": ["<PERSON><PERSON><PERSON>(v-model)", "animated", "destroyInactiveTabPane", "<PERSON><PERSON><PERSON>", "size", "tabBarGutter", "tabBarStyle", "tabPosition", "type", "addIcon", "leftExtra", "moreIcon", "renderTabBar", "rightExtra", "change", "edit", "nextClick", "prevClick", "tabClick"]}, "a-tabs-tab-pane": {"attributes": ["forceRender", "key", "tab", "closeIcon", "tab"]}, "a-tag": {"attributes": ["closable", "closeIcon", "color", "icon", "bordered", "close"]}, "a-tag-checkable-tag": {"attributes": ["checked(v-model)", "change"]}, "a-time-picker": {"attributes": ["allowClear", "autofocus", "bordered", "clearIcon", "clearText", "disabled", "disabledTime", "format", "getPopupContainer", "hideDisabledOptions", "hourStep", "inputReadOnly", "minuteStep", "open(v-model)", "placeholder", "placement", "popupClassName", "popupStyle", "renderExtraFooter", "secondStep", "showNow", "suffixIcon", "use12Hours", "value(v-model)", "valueFormat"]}, "a-timeline": {"attributes": ["mode", "pending", "pendingDot", "reverse"]}, "a-timeline-item": {"attributes": ["color", "dot", "label", "position"]}, "a-tooltip": {"attributes": ["title", "align", "arrowPointAtCenter", "autoAdjustOverflow", "color", "destroyTooltipOnHide", "getPopupContainer", "mouseEnterDelay", "mouseLeaveDelay", "overlayClassName", "overlayStyle", "overlayInnerStyle", "placement", "trigger", "open(v-model)"]}, "a-tour": {"attributes": ["arrow", "placement", "mask", "type", "open", "current(v-model)", "scrollIntoViewOptions", "<PERSON><PERSON><PERSON>", "zIndex"]}, "a-tour-step": {"attributes": ["target", "arrow", "cover", "title", "description", "placement", "mask", "type", "nextButtonProps", "prevButtonProps", "scrollIntoViewOptions"]}, "a-transfer": {"attributes": ["dataSource", "disabled", "filterOption", "footer", "listStyle", "locale", "oneWay", "operations", "operationStyle", "pagination", "render", "selectAllLabels", "selected<PERSON>eys(v-model)", "showSearch", "showSelectAll", "status", "targetKeys(v-model)", "titles"]}, "a-tree": {"attributes": ["allowDrop", "autoExpandParent", "blockNode", "checkable", "checkedKeys(v-model)", "checkStrictly", "defaultExpandAll", "disabled", "draggable", "expandedKeys(v-model)", "fieldNames", "filterTreeNode", "height", "loadData", "loadedKeys", "multiple", "selectable", "selected<PERSON>eys(v-model)", "showIcon", "showLine", "switcherIcon", "title", "treeData", "virtual", "scrollTo({ key: string | number; align?: 'top' | 'bottom' | 'auto'; offset?: number })"]}, "a-tree-node": {"attributes": ["checkable", "class", "disableCheckbox", "disabled", "icon", "<PERSON><PERSON><PERSON><PERSON>", "key", "selectable", "style", "title"]}, "a-directory-tree": {"attributes": ["expandAction"]}, "a-tree-select": {"attributes": []}, "a-typography": {"attributes": []}, "a-typography-text": {"attributes": ["code", "content(v-model)", "copyable", "delete", "disabled", "editable", "ellipsis", "keyboard", "mark", "strong", "type", "underline"]}, "a-typography-title": {"attributes": ["code", "content(v-model)", "copyable", "delete", "disabled", "editable", "ellipsis", "level", "mark", "type", "underline"]}, "a-typography-paragraph": {"attributes": ["code", "content(v-model)", "copyable", "delete", "disabled", "editable", "ellipsis", "mark", "strong", "type", "underline"]}, "a-upload": {"attributes": ["accept", "action", "beforeUpload", "customRequest", "data", "directory", "disabled", "downloadIcon", "fileList", "headers", "iconRender", "isImageUrl", "itemRender", "listType", "maxCount", "method", "multiple", "name", "openFileDialogOnClick", "previewFile", "previewIcon", "progress", "removeIcon", "showUploadList", "supportServerRender", "withCredentials"]}, "a-upload-file": {"attributes": ["crossOrigin", "name", "percent", "status", "thumbUrl", "uid", "url"]}, "a-watermark": {"attributes": ["width", "height", "rotate", "zIndex", "image", "content", "font", "gap", "offset"]}, "a-anchor-link": {"attributes": []}, "a-auto-complete-opt-group": {"attributes": []}, "a-auto-complete-option": {"attributes": []}, "a-badge": {"attributes": []}, "a-badge-ribbon": {"attributes": []}, "a-button-group": {"attributes": []}, "a-card-grid": {"attributes": []}, "a-checkable-tag": {"attributes": []}, "a-checkbox-group": {"attributes": []}, "a-col": {"attributes": []}, "a-style-provider": {"attributes": []}, "a-descriptions-item": {"attributes": []}, "a-form-item-rest": {"attributes": []}, "a-image-preview-group": {"attributes": []}, "a-layout-content": {"attributes": []}, "a-layout-footer": {"attributes": []}, "a-layout-header": {"attributes": []}, "a-locale-provider": {"attributes": []}, "a-mentions-option": {"attributes": []}, "a-month-picker": {"attributes": []}, "a-quarter-picker": {"attributes": []}, "a-radio-button": {"attributes": []}, "a-range-picker": {"attributes": []}, "a-row": {"attributes": []}, "a-select-opt-group": {"attributes": []}, "a-select-option": {"attributes": []}, "a-skeleton-avatar": {"attributes": []}, "a-skeleton-button": {"attributes": []}, "a-skeleton-image": {"attributes": []}, "a-skeleton-input": {"attributes": []}, "a-step": {"attributes": []}, "a-sub-menu": {"attributes": []}, "a-tab-pane": {"attributes": []}, "a-table-column": {"attributes": []}, "a-table-column-group": {"attributes": []}, "a-table-summary": {"attributes": []}, "a-table-summary-cell": {"attributes": []}, "a-table-summary-row": {"attributes": []}, "a-textarea": {"attributes": []}, "a-time-range-picker": {"attributes": []}, "a-tree-select-node": {"attributes": []}, "a-typography-link": {"attributes": []}, "a-upload-dragger": {"attributes": []}, "a-week-picker": {"attributes": []}, "aqr-code": {"attributes": []}, "a-float-button-group": {"attributes": []}, "a-back-top": {"attributes": []}}