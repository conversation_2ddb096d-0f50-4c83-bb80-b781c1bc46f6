import type { CSSObject } from '../_util/cssinjs';
import type { DerivativeToken } from '../theme/internal';
export { operationUnit } from './operationUnit';
export { roundedArrow } from './roundedArrow';
export { genPresetColor } from './presetColor';
export declare const textEllipsis: CSSObject;
export declare const resetComponent: (token: DerivativeToken) => CSSObject;
export declare const resetIcon: () => CSSObject;
export declare const clearFix: () => CSSObject;
export declare const genLinkStyle: (token: DerivativeToken) => CSSObject;
export declare const genCommonStyle: (token: DerivativeToken, componentPrefixCls: string) => CSSObject;
export declare const genFocusOutline: (token: DerivativeToken) => CSSObject;
export declare const genFocusStyle: (token: DerivativeToken) => CSSObject;
