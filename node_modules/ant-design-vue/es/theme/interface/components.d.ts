import type { ComponentToken as AlertComponentToken } from '../../alert/style';
import type { ComponentToken as AnchorComponentToken } from '../../anchor/style';
import type { ComponentToken as AvatarComponentToken } from '../../avatar/style';
import type { ComponentToken as ButtonComponentToken } from '../../button/style';
import type { ComponentToken as FloatButtonComponentToken } from '../../float-button/style';
import type { ComponentToken as CalendarComponentToken } from '../../calendar/style';
import type { ComponentToken as CardComponentToken } from '../../card/style';
import type { ComponentToken as CarouselComponentToken } from '../../carousel/style';
import type { ComponentToken as CascaderComponentToken } from '../../cascader/style';
import type { ComponentToken as CheckboxComponentToken } from '../../checkbox/style';
import type { ComponentToken as CollapseComponentToken } from '../../collapse/style';
import type { ComponentToken as DatePickerComponentToken } from '../../date-picker/style';
import type { ComponentToken as DividerComponentToken } from '../../divider/style';
import type { ComponentToken as DropdownComponentToken } from '../../dropdown/style';
import type { ComponentToken as DrawerComponentToken } from '../../drawer/style';
import type { ComponentToken as EmptyComponentToken } from '../../empty/style';
import type { ComponentToken as ImageComponentToken } from '../../image/style';
import type { ComponentToken as InputNumberComponentToken } from '../../input-number/style';
import type { ComponentToken as LayoutComponentToken } from '../../layout/style';
import type { ComponentToken as ListComponentToken } from '../../list/style';
import type { ComponentToken as MentionsComponentToken } from '../../mentions/style';
import type { ComponentToken as MenuComponentToken } from '../../menu/style';
import type { ComponentToken as MessageComponentToken } from '../../message/style';
import type { ComponentToken as ModalComponentToken } from '../../modal/style';
import type { ComponentToken as NotificationComponentToken } from '../../notification/style';
import type { ComponentToken as PopconfirmComponentToken } from '../../popconfirm/style';
import type { ComponentToken as PopoverComponentToken } from '../../popover/style';
import type { ComponentToken as ProgressComponentToken } from '../../progress/style';
import type { ComponentToken as RadioComponentToken } from '../../radio/style';
import type { ComponentToken as RateComponentToken } from '../../rate/style';
import type { ComponentToken as ResultComponentToken } from '../../result/style';
import type { ComponentToken as SegmentedComponentToken } from '../../segmented/style';
import type { ComponentToken as SelectComponentToken } from '../../select/style';
import type { ComponentToken as SkeletonComponentToken } from '../../skeleton/style';
import type { ComponentToken as SliderComponentToken } from '../../slider/style';
import type { ComponentToken as SpaceComponentToken } from '../../space/style';
import type { ComponentToken as SpinComponentToken } from '../../spin/style';
import type { ComponentToken as StepsComponentToken } from '../../steps/style';
import type { ComponentToken as TableComponentToken } from '../../table/style';
import type { ComponentToken as TabsComponentToken } from '../../tabs/style';
import type { ComponentToken as TagComponentToken } from '../../tag/style';
import type { ComponentToken as TimelineComponentToken } from '../../timeline/style';
import type { ComponentToken as TooltipComponentToken } from '../../tooltip/style';
import type { ComponentToken as TransferComponentToken } from '../../transfer/style';
import type { ComponentToken as TypographyComponentToken } from '../../typography/style';
import type { ComponentToken as UploadComponentToken } from '../../upload/style';
import type { ComponentToken as TourComponentToken } from '../../tour/style';
import type { ComponentToken as QRCodeComponentToken } from '../../qrcode/style';
import type { ComponentToken as AppComponentToken } from '../../app/style';
import type { ComponentToken as WaveToken } from '../../_util/wave/style';
import type { ComponentToken as FlexToken } from '../../flex/style';
export interface ComponentTokenMap {
    Affix?: {};
    Alert?: AlertComponentToken;
    Anchor?: AnchorComponentToken;
    Avatar?: AvatarComponentToken;
    Badge?: {};
    Button?: ButtonComponentToken;
    Breadcrumb?: {};
    Card?: CardComponentToken;
    Carousel?: CarouselComponentToken;
    Cascader?: CascaderComponentToken;
    Checkbox?: CheckboxComponentToken;
    Collapse?: CollapseComponentToken;
    Comment?: {};
    DatePicker?: DatePickerComponentToken;
    Descriptions?: {};
    Divider?: DividerComponentToken;
    Drawer?: DrawerComponentToken;
    Dropdown?: DropdownComponentToken;
    Empty?: EmptyComponentToken;
    FloatButton?: FloatButtonComponentToken;
    Form?: {};
    Grid?: {};
    Image?: ImageComponentToken;
    Input?: {};
    InputNumber?: InputNumberComponentToken;
    Layout?: LayoutComponentToken;
    List?: ListComponentToken;
    Mentions?: MentionsComponentToken;
    Notification?: NotificationComponentToken;
    PageHeader?: {};
    Pagination?: {};
    Popover?: PopoverComponentToken;
    Popconfirm?: PopconfirmComponentToken;
    Rate?: RateComponentToken;
    Radio?: RadioComponentToken;
    Result?: ResultComponentToken;
    Segmented?: SegmentedComponentToken;
    Select?: SelectComponentToken;
    Skeleton?: SkeletonComponentToken;
    Slider?: SliderComponentToken;
    Spin?: SpinComponentToken;
    Statistic?: {};
    Switch?: {};
    Tag?: TagComponentToken;
    Tree?: {};
    TreeSelect?: {};
    Typography?: TypographyComponentToken;
    Timeline?: TimelineComponentToken;
    Transfer?: TransferComponentToken;
    Tabs?: TabsComponentToken;
    Calendar?: CalendarComponentToken;
    Steps?: StepsComponentToken;
    Menu?: MenuComponentToken;
    Modal?: ModalComponentToken;
    Message?: MessageComponentToken;
    Upload?: UploadComponentToken;
    Tooltip?: TooltipComponentToken;
    Table?: TableComponentToken;
    Space?: SpaceComponentToken;
    Progress?: ProgressComponentToken;
    Tour?: TourComponentToken;
    QRCode?: QRCodeComponentToken;
    App?: AppComponentToken;
    Flex?: FlexToken;
    Wave?: WaveToken;
}
