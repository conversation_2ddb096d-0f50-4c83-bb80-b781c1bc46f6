declare const TransButton: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    noStyle: {
        type: BooleanConstructor;
        default: any;
    };
    onClick: FunctionConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    autofocus: {
        type: BooleanConstructor;
        default: any;
    };
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    noStyle: {
        type: BooleanConstructor;
        default: any;
    };
    onClick: FunctionConstructor;
    disabled: {
        type: BooleanConstructor;
        default: any;
    };
    autofocus: {
        type: BooleanConstructor;
        default: any;
    };
}>> & Readonly<{}>, {
    disabled: boolean;
    autofocus: boolean;
    noStyle: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default TransButton;
