import type { ValidateStatus } from '../form/FormItem';
declare const InputStatuses: readonly ["warning", "error", ""];
export type InputStatus = (typeof InputStatuses)[number];
export declare function getStatusClassNames(prefixCls: string, status?: ValidateStatus, hasFeedback?: boolean): string;
export declare const getMergedStatus: (contextStatus?: ValidateStatus, customStatus?: InputStatus) => "" | "error" | "validating" | "success" | "warning";
export {};
