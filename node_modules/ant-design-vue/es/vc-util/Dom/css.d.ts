export declare function get(node: HTMLElement, name: any): {};
export declare function set(node: HTMLElement, name: any, value: string | number): {};
export declare function getOuterWidth(el: HTMLElement): number;
export declare function getOuterHeight(el: HTMLElement): number;
export declare function getDocSize(): {
    width: number;
    height: number;
};
export declare function getClientSize(): {
    width: number;
    height: number;
};
export declare function getScroll(): {
    scrollLeft: number;
    scrollTop: number;
};
export declare function getOffset(node: any): {
    left: number;
    top: number;
};
export declare function styleToString(style: CSSStyleDeclaration): any;
export declare function styleObjectToString(style: Record<string, string>): string;
