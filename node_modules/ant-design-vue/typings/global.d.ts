/* eslint-disable @typescript-eslint/consistent-type-imports */
declare module 'vue' {
  export interface GlobalComponents {
    AAffix: typeof import('ant-design-vue')['Affix'];

    AAlert: typeof import('ant-design-vue')['Alert'];

    AAnchor: typeof import('ant-design-vue')['Anchor'];

    AAnchorLink: typeof import('ant-design-vue')['AnchorLink'];

    AAutoComplete: typeof import('ant-design-vue')['AutoComplete'];

    AAutoCompleteOptGroup: typeof import('ant-design-vue')['AutoCompleteOptGroup'];

    AAutoCompleteOption: typeof import('ant-design-vue')['AutoCompleteOption'];

    AAvatar: typeof import('ant-design-vue')['Avatar'];

    AAvatarGroup: typeof import('ant-design-vue')['AvatarGroup'];

    ABadge: typeof import('ant-design-vue')['Badge'];

    ABadgeRibbon: typeof import('ant-design-vue')['BadgeRibbon'];

    ABreadcrumb: typeof import('ant-design-vue')['Breadcrumb'];

    ABreadcrumbItem: typeof import('ant-design-vue')['BreadcrumbItem'];

    ABreadcrumbSeparator: typeof import('ant-design-vue')['BreadcrumbSeparator'];

    AButton: typeof import('ant-design-vue')['Button'];

    AButtonGroup: typeof import('ant-design-vue')['ButtonGroup'];

    ACalendar: typeof import('ant-design-vue')['Calendar'];

    ACard: typeof import('ant-design-vue')['Card'];

    ACardGrid: typeof import('ant-design-vue')['CardGrid'];

    ACardMeta: typeof import('ant-design-vue')['CardMeta'];

    ACarousel: typeof import('ant-design-vue')['Carousel'];

    ACascader: typeof import('ant-design-vue')['Cascader'];

    ACheckableTag: typeof import('ant-design-vue')['CheckableTag'];

    ACheckbox: typeof import('ant-design-vue')['Checkbox'];

    ACheckboxGroup: typeof import('ant-design-vue')['CheckboxGroup'];

    ACol: typeof import('ant-design-vue')['Col'];

    ACollapse: typeof import('ant-design-vue')['Collapse'];

    ACollapsePanel: typeof import('ant-design-vue')['CollapsePanel'];

    AComment: typeof import('ant-design-vue')['Comment'];

    AConfigProvider: typeof import('ant-design-vue')['ConfigProvider'];

    AStyleProvider: typeof import('ant-design-vue')['StyleProvider'];

    ADatePicker: typeof import('ant-design-vue')['DatePicker'];

    ADescriptions: typeof import('ant-design-vue')['Descriptions'];

    ADescriptionsItem: typeof import('ant-design-vue')['DescriptionsItem'];

    ADirectoryTree: typeof import('ant-design-vue')['DirectoryTree'];

    ADivider: typeof import('ant-design-vue')['Divider'];

    ADrawer: typeof import('ant-design-vue')['Drawer'];

    ADropdown: typeof import('ant-design-vue')['Dropdown'];

    ADropdownButton: typeof import('ant-design-vue')['DropdownButton'];

    AEmpty: typeof import('ant-design-vue')['Empty'];

    AForm: typeof import('ant-design-vue')['Form'];

    AFormItem: typeof import('ant-design-vue')['FormItem'];

    AFormItemRest: typeof import('ant-design-vue')['FormItemRest'];

    AImage: typeof import('ant-design-vue')['Image'];

    AImagePreviewGroup: typeof import('ant-design-vue')['ImagePreviewGroup'];

    AInput: typeof import('ant-design-vue')['Input'];

    AInputGroup: typeof import('ant-design-vue')['InputGroup'];

    AInputNumber: typeof import('ant-design-vue')['InputNumber'];

    AInputPassword: typeof import('ant-design-vue')['InputPassword'];

    AInputSearch: typeof import('ant-design-vue')['InputSearch'];

    ALayout: typeof import('ant-design-vue')['Layout'];

    ALayoutContent: typeof import('ant-design-vue')['LayoutContent'];

    ALayoutFooter: typeof import('ant-design-vue')['LayoutFooter'];

    ALayoutHeader: typeof import('ant-design-vue')['LayoutHeader'];

    ALayoutSider: typeof import('ant-design-vue')['LayoutSider'];

    AList: typeof import('ant-design-vue')['List'];

    AListItem: typeof import('ant-design-vue')['ListItem'];

    AListItemMeta: typeof import('ant-design-vue')['ListItemMeta'];

    ALocaleProvider: typeof import('ant-design-vue')['LocaleProvider'];

    AMentions: typeof import('ant-design-vue')['Mentions'];

    AMentionsOption: typeof import('ant-design-vue')['MentionsOption'];

    AMenu: typeof import('ant-design-vue')['Menu'];

    AMenuDivider: typeof import('ant-design-vue')['MenuDivider'];

    AMenuItem: typeof import('ant-design-vue')['MenuItem'];

    AMenuItemGroup: typeof import('ant-design-vue')['MenuItemGroup'];

    AModal: typeof import('ant-design-vue')['Modal'];

    AMonthPicker: typeof import('ant-design-vue')['MonthPicker'];

    APageHeader: typeof import('ant-design-vue')['PageHeader'];

    APagination: typeof import('ant-design-vue')['Pagination'];

    APopconfirm: typeof import('ant-design-vue')['Popconfirm'];

    APopover: typeof import('ant-design-vue')['Popover'];

    AProgress: typeof import('ant-design-vue')['Progress'];

    AQuarterPicker: typeof import('ant-design-vue')['QuarterPicker'];

    ARadio: typeof import('ant-design-vue')['Radio'];

    ARadioButton: typeof import('ant-design-vue')['RadioButton'];

    ARadioGroup: typeof import('ant-design-vue')['RadioGroup'];

    ARangePicker: typeof import('ant-design-vue')['RangePicker'];

    ARate: typeof import('ant-design-vue')['Rate'];

    AResult: typeof import('ant-design-vue')['Result'];

    ARow: typeof import('ant-design-vue')['Row'];

    ASelect: typeof import('ant-design-vue')['Select'];

    ASegmented: typeof import('ant-design-vue')['Segmented'];

    ASelectOptGroup: typeof import('ant-design-vue')['SelectOptGroup'];

    ASelectOption: typeof import('ant-design-vue')['SelectOption'];

    ASkeleton: typeof import('ant-design-vue')['Skeleton'];

    ASkeletonAvatar: typeof import('ant-design-vue')['SkeletonAvatar'];

    ASkeletonButton: typeof import('ant-design-vue')['SkeletonButton'];

    ASkeletonImage: typeof import('ant-design-vue')['SkeletonImage'];

    ASkeletonInput: typeof import('ant-design-vue')['SkeletonInput'];

    ASlider: typeof import('ant-design-vue')['Slider'];

    ASpace: typeof import('ant-design-vue')['Space'];

    ASpaceCompact: typeof import('ant-design-vue')['Compact'];

    ASpin: typeof import('ant-design-vue')['Spin'];

    AStatistic: typeof import('ant-design-vue')['Statistic'];

    AStatisticCountdown: typeof import('ant-design-vue')['StatisticCountdown'];

    AStep: typeof import('ant-design-vue')['Step'];

    ASteps: typeof import('ant-design-vue')['Steps'];

    ASubMenu: typeof import('ant-design-vue')['SubMenu'];

    ASwitch: typeof import('ant-design-vue')['Switch'];

    ATabPane: typeof import('ant-design-vue')['TabPane'];

    ATable: typeof import('ant-design-vue')['Table'];

    ATableColumn: typeof import('ant-design-vue')['TableColumn'];

    ATableColumnGroup: typeof import('ant-design-vue')['TableColumnGroup'];

    ATableSummary: typeof import('ant-design-vue')['TableSummary'];

    ATableSummaryCell: typeof import('ant-design-vue')['TableSummaryCell'];

    ATableSummaryRow: typeof import('ant-design-vue')['TableSummaryRow'];

    ATabs: typeof import('ant-design-vue')['Tabs'];

    ATag: typeof import('ant-design-vue')['Tag'];

    ATextarea: typeof import('ant-design-vue')['Textarea'];

    ATimePicker: typeof import('ant-design-vue')['TimePicker'];

    ATimeRangePicker: typeof import('ant-design-vue')['TimeRangePicker'];

    ATimeline: typeof import('ant-design-vue')['Timeline'];

    ATimelineItem: typeof import('ant-design-vue')['TimelineItem'];

    ATooltip: typeof import('ant-design-vue')['Tooltip'];

    ATransfer: typeof import('ant-design-vue')['Transfer'];

    ATree: typeof import('ant-design-vue')['Tree'];

    ATreeNode: typeof import('ant-design-vue')['TreeNode'];

    ATreeSelect: typeof import('ant-design-vue')['TreeSelect'];

    ATreeSelectNode: typeof import('ant-design-vue')['TreeSelectNode'];

    ATypography: typeof import('ant-design-vue')['Typography'];

    ATypographyLink: typeof import('ant-design-vue')['TypographyLink'];

    ATypographyParagraph: typeof import('ant-design-vue')['TypographyParagraph'];

    ATypographyText: typeof import('ant-design-vue')['TypographyText'];

    ATypographyTitle: typeof import('ant-design-vue')['TypographyTitle'];

    AUpload: typeof import('ant-design-vue')['Upload'];

    AUploadDragger: typeof import('ant-design-vue')['UploadDragger'];

    AWeekPicker: typeof import('ant-design-vue')['WeekPicker'];

    AQrCode: typeof import('ant-design-vue')['QRCode'];

    ATour: typeof import('ant-design-vue')['Tour'];

    AFloatButton: typeof import('ant-design-vue')['FloatButton'];

    AFloatButtonGroup: typeof import('ant-design-vue')['FloatButtonGroup'];

    ABackTop: typeof import('ant-design-vue')['BackTop'];

    AWatermark: typeof import('ant-design-vue')['Watermark'];

    AFlex: typeof import('ant-design-vue')['Flex'];
  }
}
export {};
