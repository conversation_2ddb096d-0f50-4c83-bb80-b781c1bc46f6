{"name": "ant-design-vue", "version": "4.2.6", "title": "Ant Design Vue", "description": "An enterprise-class UI design language and Vue-based implementation", "keywords": ["vue", "vue3", "ant", "design", "antd", "vueComponent", "component", "components", "ui", "framework", "frontend"], "main": "lib/index.js", "module": "es/index.js", "unpkg": "dist/antd.min.js", "typings": "es/index.d.ts", "files": ["dist", "lib", "es", "scripts", "vetur", "typings/global.d.ts", "locale"], "scripts": {"collect-token-statistic": "tsx scripts/collect-token-statistic.js", "token-meta": "node scripts/generate-token-meta.js", "predev": "npm run version & npm run collect-token-statistic & npm run token-meta && node node_modules/esbuild/install.js", "precompile": "npm run version & npm run collect-token-statistic & npm run token-meta", "pretest": "npm run version", "predist": "npm run version", "presite": "npm run version & npm run routes & npm run collect-token-statistic & npm run token-meta", "dev": "npm run routes && vite serve site", "fast-dev": "npm run routes && vite serve site", "test": "cross-env NODE_ENV=test jest --config .jest.js", "compile": "node antd-tools/cli/run.js compile", "generator-webtypes": "tsc -p antd-tools/generator-types/tsconfig.json && node antd-tools/generator-types/index.js", "pub": "npm run version & npm run collect-token-statistic & npm run token-meta && node --max_old_space_size=8192 antd-tools/cli/run.js pub", "pub-with-ci": "npm run version & npm run collect-token-statistic & npm run token-meta && node antd-tools/cli/run.js pub-with-ci", "prepublishOnly": "node antd-tools/cli/run.js guard", "pre-publish": "npm run generator-webtypes", "prettier": "prettier -c --write **/*", "pretty-quick": "pretty-quick", "dist": "node --max_old_space_size=8192 antd-tools/cli/run.js dist", "lint": "npm run tsc && npm run lint:demo && npm run lint:md && npm run lint:script && npm run lint:site", "lint:components": "eslint --fix --ext .jsx,.js,.ts,.tsx ./components", "lint:demo": "eslint --fix components/*/demo/*.vue", "lint:md": "eslint --fix *.md", "lint:script": "eslint . --ext '.js,.jsx,.ts,.tsx'", "lint:site": "eslint --fix -c ./.eslintrc.js --ext .jsx,.js,.ts,.tsx,vue ./site", "lint:style": "stylelint \"{site,components}/**/*.less\" --syntax less", "codecov": "codecov", "routes": "node site/scripts/genrateRoutes.js", "tsc": "tsc --noEmit", "vue-tsc": "vue-tsc --noEmit", "site": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build site --base=https://next.antdv.com/", "pub:site": "npm run site && node site/scripts/pushToOSS.js", "prepare": "husky install", "version": "node ./scripts/generate-version", "sort-api": "node antd-tools/cli/run.js sort-api-table"}, "browserslist": ["> 0.5%", "last 2 versions", "Firefox ESR", "not dead"], "repository": {"type": "git", "url": "https://github.com/vueComponent/ant-design-vue.git"}, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design-vue"}, "bugs": {"url": "https://github.com/vueComponent/ant-design-vue/issues"}, "homepage": "https://www.antdv.com/", "peerDependencies": {"vue": ">=3.2.0"}, "engines": {"node": ">=12.22.0"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.10.5", "@babel/plugin-proposal-class-properties": "^7.8.3", "@babel/plugin-proposal-export-default-from": "^7.8.3", "@babel/plugin-proposal-export-namespace-from": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.9.6", "@babel/plugin-proposal-optional-chaining": "^7.10.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-member-expression-literals": "^7.8.3", "@babel/plugin-transform-object-assign": "^7.8.3", "@babel/plugin-transform-property-literals": "^7.8.3", "@babel/plugin-transform-runtime": "^7.10.5", "@babel/plugin-transform-template-literals": "^7.8.3", "@babel/plugin-transform-typescript": "^7.12.1", "@babel/polyfill": "^7.8.7", "@babel/preset-env": "^7.9.6", "@babel/preset-typescript": "^7.10.4", "@commitlint/cli": "^12.0.0", "@commitlint/config-conventional": "^12.0.0", "@octokit/rest": "^18.0.0", "@rollup/plugin-babel": "^5.3.0", "@types/compression": "^1.7.0", "@types/fs-extra": "^9.0.8", "@types/jest": "^28.1.4", "@types/koa": "^2.11.6", "@types/lodash-es": "^4.17.3", "@types/lru-cache": "^5.1.0", "@types/markdown-it": "^10.0.2", "@types/node": "^14.0.0", "@types/postcss-load-config": "^2.0.1", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-legacy": "^2.2.0", "@vitejs/plugin-vue": "^3.0.0", "@vitejs/plugin-vue-jsx": "^2.0.0", "@vue/babel-plugin-jsx": "^1.0.0", "@vue/cli-plugin-eslint": "^5.0.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.0.2", "@vue/vue3-jest": "28", "@vueuse/core": "^8.3.1", "@webpack-cli/serve": "^1.3.1", "acorn": "^8.0.0", "ali-oss": "^6.16.0", "autoprefixer": "^10.2.0", "axios": "^0.22.0", "babel-eslint": "^10.0.1", "babel-jest": "^28.1.2", "babel-loader": "^8.0.0", "babel-plugin-import": "^1.1.1", "babel-plugin-inline-import-data-uri": "^1.0.1", "babel-plugin-istanbul": "^6.0.0", "babel-plugin-transform-require-context": "^0.1.1", "case-sensitive-paths-webpack-plugin": "^2.1.2", "chalk": "^4.1.1", "cheerio": "^1.0.0-rc.2", "codecov": "^3.0.0", "codesandbox": "^2.2.3", "colorful": "^2.1.0", "commander": "^6.1.0", "compare-versions": "^3.3.0", "cross-env": "^7.0.0", "css-loader": "^5.0.0", "css-minimizer-webpack-plugin": "^3.0.0", "cz-git": "^1.3.8", "date-fns": "^2.24.0", "diacritics": "^1.3.0", "docsearch.js": "^2.6.3", "duplicate-package-checker-webpack-plugin": "^3.0.0", "enquire-js": "^0.2.1", "esbuild": "~0.12.29", "esbuild-loader": "^3.0.0", "escape-html": "^1.0.3", "eslint": "^8.3.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-html": "^6.0.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jest": "^26.0.0", "eslint-plugin-markdown": "^2.0.0", "eslint-plugin-no-explicit-type-exports": "^0.12.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "^9.17.0", "fast-glob": "^3.2.7", "fetch-jsonp": "^1.1.3", "fs-extra": "^10.0.0", "glob": "^7.1.2", "globby": "^11.1.0", "gray-matter": "^4.0.3", "gulp": "^4.0.1", "gulp-babel": "^8.0.0", "gulp-strip-code": "^0.1.4", "gulp-typescript": "^6.0.0-alpha.1", "html-webpack-plugin": "^5.3.1", "husky": "^6.0.0", "ignore-emit-webpack-plugin": "^2.0.6", "is-windows": "^1.0.2", "jest": "^28.1.2", "jest-environment-jsdom": "^28.0.0", "jest-environment-node": "^28.0.2", "jest-serializer-vue": "^2.0.0", "jest-transform-stub": "^2.0.0", "js-base64": "^3.0.0", "json-templater": "^1.2.0", "jsonp": "^0.2.1", "less": "^4.0.0", "less-loader": "^10.0.0", "less-plugin-npm-import": "^2.1.0", "less-vars-to-js": "^1.3.0", "lint-staged": "^11.0.0", "majo": "^0.10.1", "markdown-it": "^8.4.2", "markdown-it-anchor": "^8.0.4", "markdown-it-container": "^3.0.0", "markdown-it-emoji": "^2.0.0", "markdown-it-table-of-contents": "^0.5.2", "marked": "0.3.18", "merge2": "^1.2.1", "mini-css-extract-plugin": "^2.4.5", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "mockdate": "^2.0.2", "moment": "^2.29.1", "nprogress": "^0.2.0", "postcss": "^8.2.12", "postcss-loader": "^6.0.0", "prettier": "^2.2.0", "pretty-quick": "^3.0.0", "prismjs": "^1.23.0", "progress": "^2.0.3", "raw-loader": "^4.0.2", "remark-frontmatter": "^2.0.0", "remark-parse": "^8.0.0", "remark-stringify": "^8.0.0", "remark-yaml-config": "^4.1.0", "remove-files-webpack-plugin": "^1.5.0", "reqwest": "^2.0.5", "rimraf": "^3.0.0", "rucksack-css": "^1.0.2", "selenium-server": "^3.0.1", "semver": "^7.0.0", "slash": "^3.0.0", "string-replace-loader": "^3.1.0", "style-loader": "^3.0.0", "stylelint": "^14.0.0", "stylelint-config-prettier": "^9.0.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^25.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-order": "^5.0.0", "terser-webpack-plugin": "^5.1.1", "through2": "^3.0.0", "tinycolor2": "^1.6.0", "ts-jest": "^28.0.5", "ts-loader": "^9.1.0", "tsx": "^3.12.10", "typedoc": "^0.23.25", "typescript": "~4.9.3", "umi-request": "^1.3.5", "unified": "9.2.2", "url-loader": "^3.0.0", "vanilla-jsoneditor": "^0.15.1", "vite": "^3.0.0", "vue": "^3.2.0", "vue-clipboard2": "0.3.3", "vue-drag-resize": "^2.0.3", "vue-eslint-parser": "^9.3.1", "vue-i18n": "^9.1.7", "vue-infinite-scroll": "^2.0.2", "vue-loader": "^17.0.0", "vue-request": "^1.0.2", "vue-router": "^4.0.0", "vue-style-loader": "^4.1.2", "vue-tsc": "^1.0.6", "vuex": "^4.0.0", "webpack": "^5.0.0", "webpack-bundle-analyzer": "^4.4.2", "webpack-cli": "^4.6.0", "webpack-dev-server": "^4.0.0", "webpack-merge": "^5.0.0", "webpackbar": "^5.0.2", "xhr-mock": "^2.5.1"}, "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^7.0.0", "@babel/runtime": "^7.10.5", "@ctrl/tinycolor": "^3.5.0", "@emotion/hash": "^0.9.0", "@emotion/unitless": "^0.8.0", "@simonwep/pickr": "~1.8.0", "array-tree-filter": "^2.1.0", "async-validator": "^4.0.0", "csstype": "^3.1.1", "dayjs": "^1.10.5", "dom-align": "^1.12.1", "dom-scroll-into-view": "^2.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.15", "resize-observer-polyfill": "^1.5.1", "scroll-into-view-if-needed": "^2.2.25", "shallow-equal": "^1.0.0", "stylis": "^4.1.3", "throttle-debounce": "^5.0.0", "vue-types": "^3.0.0", "warning": "^4.0.0"}, "sideEffects": ["site/*", "*.vue", "*.md", "dist/*", "*.css"], "config": {"commitizen": {"path": "node_modules/cz-git", "czConfig": "./scripts/commitizen.js"}}, "web-types": "vetur/web-types.json"}