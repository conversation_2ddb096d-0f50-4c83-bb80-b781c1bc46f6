<template>
  <a-layout-sider
    v-model:collapsed="collapsed"
    :trigger="null"
    collapsible
    width="240"
    class="sidebar"
  >
    <!-- Logo区域 -->
    <div class="logo">
      <div class="logo-icon">
        <file-protect-outlined />
      </div>
      <div v-if="!collapsed" class="logo-text">
        合同智能审查系统
      </div>
    </div>

    <!-- 导航菜单 -->
    <a-menu
      v-model:selectedKeys="selectedKeys"
      mode="inline"
      theme="dark"
      class="sidebar-menu"
    >
      <a-menu-item key="contract-review">
        <template #icon>
          <audit-outlined />
        </template>
        <router-link to="/contract-review">合同审查</router-link>
      </a-menu-item>

      <a-menu-item key="audit-report">
        <template #icon>
          <file-text-outlined />
        </template>
        <router-link to="/audit-report">审核报告</router-link>
      </a-menu-item>

      <a-menu-item key="contract-compare">
        <template #icon>
          <diff-outlined />
        </template>
        <router-link to="/contract-compare">高本对比</router-link>
      </a-menu-item>

      <a-menu-item key="precise-management">
        <template #icon>
          <control-outlined />
        </template>
        <router-link to="/precise-management">精确管理</router-link>
      </a-menu-item>

      <a-menu-item key="system-settings">
        <template #icon>
          <setting-outlined />
        </template>
        <router-link to="/system-settings">系统设置</router-link>
      </a-menu-item>
    </a-menu>

    <!-- 折叠按钮 -->
    <div class="collapse-trigger" @click="toggleCollapsed">
      <menu-unfold-outlined v-if="collapsed" />
      <menu-fold-outlined v-else />
    </div>
  </a-layout-sider>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  FileProtectOutlined,
  AuditOutlined,
  FileTextOutlined,
  DiffOutlined,
  ControlOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined
} from '@ant-design/icons-vue'

const route = useRoute()
const collapsed = ref(false)
const selectedKeys = ref<string[]>([])

// 根据当前路由设置选中的菜单项
watch(
  () => route.path,
  (newPath) => {
    if (newPath.includes('contract-review')) {
      selectedKeys.value = ['contract-review']
    } else if (newPath.includes('audit-report')) {
      selectedKeys.value = ['audit-report']
    } else if (newPath.includes('contract-compare')) {
      selectedKeys.value = ['contract-compare']
    } else if (newPath.includes('precise-management')) {
      selectedKeys.value = ['precise-management']
    } else if (newPath.includes('system-settings')) {
      selectedKeys.value = ['system-settings']
    } else {
      selectedKeys.value = ['contract-compare'] // 默认选中合同对比
    }
  },
  { immediate: true }
)

const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}
</script>

<style scoped>
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  background: #001529 !important;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.1);
  margin-bottom: 1px;
}

.logo-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 8px;
}

.logo-text {
  color: white;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
}

.sidebar-menu {
  border-right: none;
  background: transparent;
}

.sidebar-menu :deep(.ant-menu-item) {
  margin: 4px 8px;
  border-radius: 6px;
  height: 48px;
  line-height: 48px;
}

.sidebar-menu :deep(.ant-menu-item a) {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  display: block;
  width: 100%;
  height: 100%;
}

.sidebar-menu :deep(.ant-menu-item:hover) {
  background: rgba(24, 144, 255, 0.1);
}

.sidebar-menu :deep(.ant-menu-item:hover a) {
  color: #1890ff;
}

.sidebar-menu :deep(.ant-menu-item-selected) {
  background: #1890ff !important;
}

.sidebar-menu :deep(.ant-menu-item-selected a) {
  color: white !important;
}

.sidebar-menu :deep(.ant-menu-item-icon) {
  font-size: 16px;
}

.collapse-trigger {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.collapse-trigger:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}
</style>
