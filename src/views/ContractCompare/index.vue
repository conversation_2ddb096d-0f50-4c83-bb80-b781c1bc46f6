<template>
  <div class="contract-compare">
    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <FileUpload />
      </div>

      <!-- 文件对比表 -->
      <div class="file-compare-section">
        <FileList />
      </div>

      <!-- 风险统计概览 -->
      <div class="risk-overview-section">
        <RiskOverview />
      </div>

      <!-- 对比结果展示 -->
      <div v-if="hasResult" class="result-section">
        <CompareResult />
        <RiskAssessment />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useContractCompareStore } from '@/stores/contractCompare'
import FileUpload from './FileUpload.vue'
import FileList from './FileList.vue'
import RiskOverview from './RiskOverview.vue'
import CompareResult from './CompareResult.vue'
import RiskAssessment from './RiskAssessment.vue'

const store = useContractCompareStore()

const hasResult = computed(() => store.hasResult)
</script>

<style scoped>
.contract-compare {
  padding: 32px;
  background: #f5f7fa;
  min-height: 100vh;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
}

.upload-section {
  margin-bottom: 32px;
}

.file-compare-section {
  margin-bottom: 32px;
}

.risk-overview-section {
  margin-bottom: 32px;
}

.result-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

@media (max-width: 768px) {
  .contract-compare {
    padding: 16px;
  }
}
</style>
