<template>
  <div class="contract-compare-container">
    <!-- 侧边导航 -->
    <div class="sidebar">
      <div class="logo-section">
        <div class="logo">
          <file-text-outlined />
          <span>合同智能审核系统</span>
        </div>
        <div class="user-info">
          <span>法务部</span>
          <span>张律师</span>
          <a-avatar size="small">张</a-avatar>
        </div>
      </div>
      
      <a-menu mode="inline" :selected-keys="['compare']" class="nav-menu">
        <a-menu-item key="review">
          <file-text-outlined />
          <span>合同审核</span>
        </a-menu-item>
        <a-menu-item key="report">
          <bar-chart-outlined />
          <span>审核报告</span>
        </a-menu-item>
        <a-menu-item key="extract">
          <search-outlined />
          <span>要素对比</span>
        </a-menu-item>
        <a-menu-item key="manage">
          <folder-outlined />
          <span>档案管理</span>
        </a-menu-item>
        <a-menu-item key="compare" class="active">
          <diff-outlined />
          <span>差异对比</span>
        </a-menu-item>
      </a-menu>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 文件上传和管理 -->
      <div v-if="!hasCompareResult" class="upload-phase">
        <FileUpload @compare-completed="onCompareCompleted" />
        
        <!-- 已上传文件列表 -->
        <div v-if="uploadedFiles.length > 0" class="uploaded-files-section">
          <div class="section-header">
            <file-text-outlined />
            <span>待审核文件对比表</span>
          </div>
          
          <div class="file-list">
            <div 
              v-for="file in uploadedFiles" 
              :key="file.id"
              class="file-item"
              :class="{ selected: selectedFiles.includes(file.id) }"
              @click="toggleFileSelection(file.id)"
            >
              <div class="file-icon">
                <file-pdf-outlined v-if="file.type === 'pdf'" />
                <file-word-outlined v-else-if="file.type === 'doc'" />
                <file-image-outlined v-else />
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-meta">{{ file.size }} • {{ file.uploadTime }}</div>
              </div>
              <div class="file-status">
                <a-tag v-if="selectedFiles.includes(file.id)" color="blue">已选择</a-tag>
                <a-tag v-else color="default">待选择</a-tag>
              </div>
            </div>
          </div>
          
          <!-- 对比操作 -->
          <div v-if="selectedFiles.length === 2" class="compare-section">
            <a-button 
              type="primary" 
              size="large" 
              @click="startCompare"
              :loading="loading"
              class="compare-btn"
            >
              <diff-outlined />
              开始智能对比分析
            </a-button>
          </div>
        </div>

        <!-- 风险统计面板 -->
        <div class="statistics-section">
          <div class="section-header">
            <bar-chart-outlined />
            <span>风险统计概览</span>
          </div>
          
          <div class="stats-grid">
            <div class="stat-card high-risk">
              <div class="stat-number">3</div>
              <div class="stat-label">高风险条款</div>
            </div>
            
            <div class="stat-card medium-risk">
              <div class="stat-number">7</div>
              <div class="stat-label">中等风险条款</div>
            </div>
            
            <div class="stat-card low-risk">
              <div class="stat-number">12</div>
              <div class="stat-label">低风险条款</div>
            </div>
            
            <div class="stat-card overall">
              <div class="stat-number">89%</div>
              <div class="stat-label">整体合规度</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 对比结果展示 -->
      <div v-else class="result-phase">
        <CompareResult />
        <div class="back-to-upload">
          <a-button @click="backToUpload">
            <arrow-left-outlined />
            返回文件上传
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { 
  FileTextOutlined, 
  BarChartOutlined, 
  SearchOutlined, 
  FolderOutlined, 
  DiffOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileImageOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons-vue'
import { useContractCompareStore } from '@/stores/contractCompare'
import FileUpload from './FileUpload.vue'
import CompareResult from './CompareResult.vue'

const store = useContractCompareStore()

const selectedFiles = ref<string[]>([])

const uploadedFiles = computed(() => store.uploadedFiles)
const hasCompareResult = computed(() => store.compareResult !== null)
const loading = computed(() => store.loading)

const toggleFileSelection = (fileId: string) => {
  const index = selectedFiles.value.indexOf(fileId)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else if (selectedFiles.value.length < 2) {
    selectedFiles.value.push(fileId)
  } else {
    message.warning('最多只能选择两个文件进行对比')
  }
}

const startCompare = async () => {
  if (selectedFiles.value.length !== 2) {
    message.warning('请选择两个文件进行对比')
    return
  }
  
  try {
    await store.compareContracts(selectedFiles.value[0], selectedFiles.value[1])
    message.success('对比分析完成！')
  } catch (error) {
    message.error('对比失败，请重试')
  }
}

const onCompareCompleted = () => {
  // 文件上传组件完成对比后的回调
}

const backToUpload = () => {
  store.compareResult = null
  selectedFiles.value = []
}
</script>

<style scoped>
.contract-compare-container {
  display: flex;
  min-height: 100vh;
  background: #f5f7fa;
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: #ffffff;
  border-right: 1px solid #e8eaec;
  display: flex;
  flex-direction: column;
}

.logo-section {
  padding: 24px 20px;
  border-bottom: 1px solid #e8eaec;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1B4B8C;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #718096;
}

.nav-menu {
  border: none;
  padding: 16px 0;
}

.nav-menu .ant-menu-item {
  margin: 4px 16px;
  border-radius: 8px;
  height: 44px;
  line-height: 44px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-menu .ant-menu-item.active {
  background: #1B4B8C;
  color: white;
}

/* 主内容区样式 */
.main-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

/* 文件列表区域 */
.uploaded-files-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 16px;
}

.file-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background: #fafbfc;
}

.file-item.selected {
  background: rgba(27, 75, 140, 0.05);
  border-left: 4px solid #1B4B8C;
}

.file-icon {
  font-size: 24px;
  color: #1B4B8C;
  margin-right: 16px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #718096;
}

.file-status {
  margin-left: 16px;
}

/* 对比操作区域 */
.compare-section {
  text-align: center;
  padding: 32px;
  background: rgba(27, 75, 140, 0.05);
  border-radius: 12px;
  margin-bottom: 32px;
}

.compare-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 500;
}

/* 统计面板 */
.statistics-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-top: 20px;
}

.stat-card {
  text-align: center;
  padding: 24px 16px;
  border-radius: 8px;
  background: #fafbfc;
}

.stat-card.high-risk {
  background: rgba(229, 62, 62, 0.1);
}

.stat-card.medium-risk {
  background: rgba(246, 173, 85, 0.1);
}

.stat-card.low-risk {
  background: rgba(56, 161, 105, 0.1);
}

.stat-card.overall {
  background: rgba(27, 75, 140, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 8px;
}

.stat-card.high-risk .stat-number {
  color: #E53E3E;
}

.stat-card.medium-risk .stat-number {
  color: #F6AD55;
}

.stat-card.low-risk .stat-number {
  color: #38A169;
}

.stat-card.overall .stat-number {
  color: #1B4B8C;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.back-to-upload {
  margin-top: 24px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .contract-compare-container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
