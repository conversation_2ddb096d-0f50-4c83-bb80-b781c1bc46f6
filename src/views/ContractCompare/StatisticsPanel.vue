<template>
  <a-card title="对比统计" :bordered="false" class="statistics-panel">
    <a-row :gutter="16">
      <a-col :span="6">
        <a-statistic
          title="总变更数"
          :value="statistics?.totalChanges || 0"
          :value-style="{ color: '#1890ff' }"
        >
          <template #prefix>
            <swap-outlined />
          </template>
        </a-statistic>
      </a-col>
      
      <a-col :span="6">
        <a-statistic
          title="新增条款"
          :value="statistics?.addedCount || 0"
          :value-style="{ color: '#52c41a' }"
        >
          <template #prefix>
            <plus-circle-outlined />
          </template>
        </a-statistic>
      </a-col>
      
      <a-col :span="6">
        <a-statistic
          title="删除条款"
          :value="statistics?.deletedCount || 0"
          :value-style="{ color: '#ff4d4f' }"
        >
          <template #prefix>
            <minus-circle-outlined />
          </template>
        </a-statistic>
      </a-col>
      
      <a-col :span="6">
        <a-statistic
          title="修改条款"
          :value="statistics?.modifiedCount || 0"
          :value-style="{ color: '#faad14' }"
        >
          <template #prefix>
            <edit-outlined />
          </template>
        </a-statistic>
      </a-col>
    </a-row>

    <a-divider />

    <!-- 风险等级分布 -->
    <div class="risk-distribution">
      <h4>风险等级分布</h4>
      <a-row :gutter="16">
        <a-col :span="8">
          <div class="risk-item high-risk">
            <div class="risk-count">{{ statistics?.riskDistribution.high || 0 }}</div>
            <div class="risk-label">高风险</div>
          </div>
        </a-col>
        
        <a-col :span="8">
          <div class="risk-item medium-risk">
            <div class="risk-count">{{ statistics?.riskDistribution.medium || 0 }}</div>
            <div class="risk-label">中风险</div>
          </div>
        </a-col>
        
        <a-col :span="8">
          <div class="risk-item low-risk">
            <div class="risk-count">{{ statistics?.riskDistribution.low || 0 }}</div>
            <div class="risk-label">低风险</div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 对比文件信息 -->
    <a-divider />
    <div class="file-info" v-if="compareResult">
      <h4>对比文件</h4>
      <a-row :gutter="16">
        <a-col :span="12">
          <div class="file-card">
            <file-text-outlined class="file-icon" />
            <div class="file-details">
              <div class="file-name">{{ compareResult.file1.name }}</div>
              <div class="file-meta">文件A</div>
            </div>
          </div>
        </a-col>
        
        <a-col :span="12">
          <div class="file-card">
            <file-text-outlined class="file-icon" />
            <div class="file-details">
              <div class="file-name">{{ compareResult.file2.name }}</div>
              <div class="file-meta">文件B</div>
            </div>
          </div>
        </a-col>
      </a-row>
      
      <div class="compare-time">
        对比时间：{{ formatTime(compareResult.compareTime) }}
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  SwapOutlined, 
  PlusCircleOutlined, 
  MinusCircleOutlined, 
  EditOutlined,
  FileTextOutlined 
} from '@ant-design/icons-vue'
import { useContractCompareStore } from '@/stores/contractCompare'

const store = useContractCompareStore()

const compareResult = computed(() => store.compareResult)
const statistics = computed(() => compareResult.value?.statistics)

// 格式化时间
const formatTime = (timeString: string): string => {
  return new Date(timeString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.statistics-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.risk-distribution {
  margin-top: 16px;
}

.risk-distribution h4 {
  margin-bottom: 16px;
  color: #333;
}

.risk-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  border: 2px solid;
  transition: all 0.3s ease;
}

.risk-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.high-risk {
  border-color: #ff4d4f;
  background: #fff2f0;
}

.medium-risk {
  border-color: #faad14;
  background: #fffbe6;
}

.low-risk {
  border-color: #52c41a;
  background: #f6ffed;
}

.risk-count {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.high-risk .risk-count {
  color: #ff4d4f;
}

.medium-risk .risk-count {
  color: #faad14;
}

.low-risk .risk-count {
  color: #52c41a;
}

.risk-label {
  font-size: 14px;
  color: #666;
}

.file-info h4 {
  margin-bottom: 16px;
  color: #333;
}

.file-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.file-icon {
  font-size: 20px;
  color: #1890ff;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #666;
}

.compare-time {
  margin-top: 12px;
  font-size: 12px;
  color: #999;
  text-align: center;
}
</style>
