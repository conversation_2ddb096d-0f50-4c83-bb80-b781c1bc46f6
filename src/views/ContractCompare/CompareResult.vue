<template>
  <div class="compare-result-container">
    <div class="result-header">
      <div class="header-info">
        <h2>对比分析结果</h2>
        <p class="compare-files">
          {{ file1Name }} <arrow-right-outlined /> {{ file2Name }}
        </p>
      </div>
      <div class="header-actions">
        <a-button type="default" @click="exportReport">
          <export-outlined />
          导出报告
        </a-button>
        <a-button type="primary" @click="downloadPdf">
          <download-outlined />
          下载PDF
        </a-button>
      </div>
    </div>

    <!-- 对比统计概览 -->
    <div class="statistics-overview">
      <div class="stat-item">
        <div class="stat-number total">{{ statistics.totalChanges }}</div>
        <div class="stat-label">总变更数</div>
      </div>
      <div class="stat-item">
        <div class="stat-number added">{{ statistics.addedClauses }}</div>
        <div class="stat-label">新增条款</div>
      </div>
      <div class="stat-item">
        <div class="stat-number modified">{{ statistics.modifiedClauses }}</div>
        <div class="stat-label">修改条款</div>
      </div>
      <div class="stat-item">
        <div class="stat-number deleted">{{ statistics.deletedClauses }}</div>
        <div class="stat-label">删除条款</div>
      </div>
    </div>

    <!-- 风险等级分布 -->
    <div class="risk-distribution">
      <h3>风险等级分布</h3>
      <div class="risk-chart">
        <div class="risk-item high-risk">
          <div class="risk-bar" :style="{ width: `${riskPercentages.high}%` }"></div>
          <span class="risk-label">高风险 ({{ highRiskCount }})</span>
        </div>
        <div class="risk-item medium-risk">
          <div class="risk-bar" :style="{ width: `${riskPercentages.medium}%` }"></div>
          <span class="risk-label">中风险 ({{ mediumRiskCount }})</span>
        </div>
        <div class="risk-item low-risk">
          <div class="risk-bar" :style="{ width: `${riskPercentages.low}%` }"></div>
          <span class="risk-label">低风险 ({{ lowRiskCount }})</span>
        </div>
      </div>
    </div>

    <!-- 详细差异列表 -->
    <div class="differences-section">
      <div class="section-header">
        <h3>详细差异分析</h3>
        <div class="filter-controls">
          <a-select v-model:value="selectedRiskFilter" style="width: 120px" @change="filterDifferences">
            <a-select-option value="all">全部风险</a-select-option>
            <a-select-option value="high">高风险</a-select-option>
            <a-select-option value="medium">中风险</a-select-option>
            <a-select-option value="low">低风险</a-select-option>
          </a-select>
          <a-select v-model:value="selectedTypeFilter" style="width: 120px" @change="filterDifferences">
            <a-select-option value="all">全部类型</a-select-option>
            <a-select-option value="added">新增</a-select-option>
            <a-select-option value="modified">修改</a-select-option>
            <a-select-option value="deleted">删除</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="differences-list">
        <div 
          v-for="(diff, index) in filteredDifferences" 
          :key="index"
          class="difference-item"
          :class="[`type-${diff.type}`, `risk-${diff.riskLevel}`]"
        >
          <div class="diff-header">
            <div class="diff-badges">
              <a-tag :color="getTypeColor(diff.type)" class="type-badge">
                <component :is="getTypeIcon(diff.type)" />
                {{ getTypeText(diff.type) }}
              </a-tag>
              <a-tag :color="getRiskColor(diff.riskLevel)" class="risk-badge">
                {{ getRiskText(diff.riskLevel) }}
              </a-tag>
            </div>
            <div class="diff-position">{{ diff.position.section }}</div>
          </div>
          
          <div class="diff-content">
            <p>{{ diff.content }}</p>
          </div>
          
          <div class="diff-actions">
            <a-button type="text" size="small">
              <eye-outlined />
              查看详情
            </a-button>
            <a-button type="text" size="small">
              <comment-outlined />
              添加备注
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  ArrowRightOutlined,
  ExportOutlined,
  DownloadOutlined,
  EyeOutlined,
  CommentOutlined,
  PlusOutlined,
  EditOutlined,
  MinusOutlined
} from '@ant-design/icons-vue'
import { useContractCompareStore } from '@/stores/contractCompare'

const store = useContractCompareStore()

const selectedRiskFilter = ref('all')
const selectedTypeFilter = ref('all')

const file1Name = ref('销售合同_2024_001.pdf')
const file2Name = ref('服务协议_修订7版.pdf')

const statistics = computed(() => store.compareResult?.statistics || {
  totalChanges: 0,
  addedClauses: 0,
  modifiedClauses: 0,
  deletedClauses: 0
})

const differences = computed(() => store.compareResult?.differences || [])

const filteredDifferences = computed(() => {
  let filtered = differences.value
  
  if (selectedRiskFilter.value !== 'all') {
    filtered = filtered.filter(diff => diff.riskLevel === selectedRiskFilter.value)
  }
  
  if (selectedTypeFilter.value !== 'all') {
    filtered = filtered.filter(diff => diff.type === selectedTypeFilter.value)
  }
  
  return filtered
})

const highRiskCount = computed(() => differences.value.filter(d => d.riskLevel === 'high').length)
const mediumRiskCount = computed(() => differences.value.filter(d => d.riskLevel === 'medium').length)
const lowRiskCount = computed(() => differences.value.filter(d => d.riskLevel === 'low').length)

const riskPercentages = computed(() => {
  const total = differences.value.length || 1
  return {
    high: (highRiskCount.value / total) * 100,
    medium: (mediumRiskCount.value / total) * 100,
    low: (lowRiskCount.value / total) * 100
  }
})

const getTypeColor = (type: string) => {
  const colors = {
    added: 'green',
    modified: 'orange',
    deleted: 'red'
  }
  return colors[type as keyof typeof colors] || 'default'
}

const getTypeIcon = (type: string) => {
  const icons = {
    added: PlusOutlined,
    modified: EditOutlined,
    deleted: MinusOutlined
  }
  return icons[type as keyof typeof icons] || PlusOutlined
}

const getTypeText = (type: string) => {
  const texts = {
    added: '新增',
    modified: '修改',
    deleted: '删除'
  }
  return texts[type as keyof typeof texts] || '未知'
}

const getRiskColor = (risk: string) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[risk as keyof typeof colors] || 'default'
}

const getRiskText = (risk: string) => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[risk as keyof typeof texts] || '未知'
}

const filterDifferences = () => {
  // 过滤逻辑已在计算属性中处理
}

const exportReport = () => {
  console.log('导出报告')
}

const downloadPdf = () => {
  console.log('下载PDF')
}
</script>

<style scoped>
.compare-result-container {
  padding: 24px;
  background: white;
  border-radius: 12px;
  margin: 24px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.header-info h2 {
  color: #2D3748;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.compare-files {
  color: #718096;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.statistics-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.stat-item {
  text-align: center;
  padding: 24px;
  background: #fafbfc;
  border-radius: 8px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-number.total { color: #1B4B8C; }
.stat-number.added { color: #38A169; }
.stat-number.modified { color: #F6AD55; }
.stat-number.deleted { color: #E53E3E; }

.stat-label {
  color: #718096;
  font-size: 14px;
  font-weight: 500;
}

.risk-distribution {
  margin-bottom: 32px;
}

.risk-distribution h3 {
  color: #2D3748;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.risk-chart {
  background: #fafbfc;
  padding: 24px;
  border-radius: 8px;
}

.risk-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.risk-item:last-child {
  margin-bottom: 0;
}

.risk-bar {
  height: 24px;
  border-radius: 12px;
  margin-right: 12px;
  transition: width 0.3s ease;
}

.risk-item.high-risk .risk-bar { background: #E53E3E; }
.risk-item.medium-risk .risk-bar { background: #F6AD55; }
.risk-item.low-risk .risk-bar { background: #38A169; }

.risk-label {
  font-size: 14px;
  color: #2D3748;
  font-weight: 500;
}

.differences-section {
  margin-top: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  color: #2D3748;
  font-size: 18px;
  font-weight: 600;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.differences-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.difference-item {
  background: #fafbfc;
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
}

.difference-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.difference-item.risk-high {
  border-left: 4px solid #E53E3E;
}

.difference-item.risk-medium {
  border-left: 4px solid #F6AD55;
}

.difference-item.risk-low {
  border-left: 4px solid #38A169;
}

.diff-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.diff-badges {
  display: flex;
  gap: 8px;
}

.diff-position {
  color: #718096;
  font-size: 12px;
}

.diff-content p {
  color: #2D3748;
  line-height: 1.6;
  margin-bottom: 16px;
}

.diff-actions {
  display: flex;
  gap: 8px;
}
</style>
