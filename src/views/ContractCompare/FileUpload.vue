<template>
  <div class="file-upload-container">
    <a-card title="文件上传" :bordered="false">
      <div class="upload-area">
        <a-upload-dragger
          v-model:file-list="fileList"
          name="file"
          :multiple="false"
          :before-upload="beforeUpload"
          :custom-request="customUpload"
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          :show-upload-list="false"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">
            支持 PDF、Word、图片格式，单个文件不超过50MB
          </p>
        </a-upload-dragger>
      </div>

      <!-- 已上传文件列表 -->
      <div v-if="uploadedFiles.length > 0" class="uploaded-files">
        <h4>已上传文件 ({{ uploadedFiles.length }}/2)</h4>
        <div class="file-list">
          <div 
            v-for="file in uploadedFiles" 
            :key="file.id"
            class="file-item"
          >
            <div class="file-info">
              <file-text-outlined class="file-icon" />
              <div class="file-details">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-meta">
                  {{ formatFileSize(file.size) }} • {{ formatTime(file.uploadTime) }}
                </div>
              </div>
            </div>
            <a-button 
              type="text" 
              danger 
              size="small"
              @click="removeFile(file.id)"
            >
              <delete-outlined />
            </a-button>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button 
          type="primary" 
          size="large"
          :disabled="!canCompare || isLoading"
          :loading="isLoading"
          @click="startCompare"
          block
        >
          {{ isLoading ? '对比中...' : '开始对比' }}
        </a-button>
        
        <a-button 
          v-if="uploadedFiles.length > 0"
          @click="clearAllFiles"
          block
          class="mt-2"
        >
          清空文件
        </a-button>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { InboxOutlined, FileTextOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { useContractCompareStore } from '@/stores/contractCompare'
import { uploadContract, compareContracts, getRiskAssessment } from '@/api/contractCompare'

const store = useContractCompareStore()
const fileList = ref([])

const uploadedFiles = computed(() => store.uploadedFiles)
const canCompare = computed(() => store.canCompare)
const isLoading = computed(() => store.isLoading)

// 上传前验证
const beforeUpload = (file: File) => {
  const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png'].includes(file.type)
  if (!isValidType) {
    message.error('只支持 PDF、Word、图片格式文件！')
    return false
  }

  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    message.error('文件大小不能超过 50MB！')
    return false
  }

  if (uploadedFiles.value.length >= 2) {
    message.error('最多只能上传2个文件！')
    return false
  }

  return true
}

// 自定义上传
const customUpload = async ({ file }: any) => {
  try {
    store.setLoading(true)
    const contractFile = await uploadContract(file)
    store.addFile(contractFile)
    message.success(`${file.name} 上传成功！`)
  } catch (error) {
    message.error('上传失败，请重试')
    console.error('Upload error:', error)
  } finally {
    store.setLoading(false)
  }
}

// 移除文件
const removeFile = (fileId: string) => {
  store.removeFile(fileId)
  message.info('文件已移除')
}

// 清空所有文件
const clearAllFiles = () => {
  store.clearFiles()
  message.info('已清空所有文件')
}

// 开始对比
const startCompare = async () => {
  if (uploadedFiles.value.length < 2) {
    message.warning('请上传两个文件进行对比')
    return
  }

  try {
    store.setLoading(true)
    store.setError(null)
    
    const [file1, file2] = uploadedFiles.value
    const compareResult = await compareContracts(file1.id, file2.id)
    store.setCompareResult(compareResult)
    
    // 获取风险评估
    const riskAssessment = await getRiskAssessment(compareResult.id)
    store.setRiskAssessment(riskAssessment)
    
    message.success('对比完成！')
  } catch (error) {
    store.setError('对比失败，请重试')
    message.error('对比失败，请重试')
    console.error('Compare error:', error)
  } finally {
    store.setLoading(false)
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (timeString: string): string => {
  return new Date(timeString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.file-upload-container {
  height: 100%;
}

.upload-area {
  margin-bottom: 24px;
}

.uploaded-files {
  margin-bottom: 24px;
}

.uploaded-files h4 {
  margin-bottom: 12px;
  color: #333;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 20px;
  color: #1890ff;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #666;
}

.action-buttons {
  margin-top: 16px;
}

.mt-2 {
  margin-top: 8px;
}
</style>
