<template>
  <div class="file-upload-area">
    <!-- 大型拖拽上传区域 -->
    <div
      class="upload-zone"
      :class="{ 'drag-over': isDragOver }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <div class="upload-content">
        <div class="upload-icon">
          <file-add-outlined />
        </div>
        <div class="upload-text">
          <div class="primary-text">请将PDF合同文件拖拽此处，或点击选择文件</div>
          <div class="secondary-text">支持格式：PDF、Word、图片，单个文件不超过50MB</div>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
      @change="handleFileSelect"
      style="display: none"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { FileAddOutlined } from '@ant-design/icons-vue'
import { useContractCompareStore } from '@/stores/contractCompare'
import { uploadContract } from '@/api/contractCompare'

const store = useContractCompareStore()
const fileInput = ref<HTMLInputElement>()
const isDragOver = ref(false)

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files) {
    handleFiles(Array.from(files))
  }
}

// 处理拖拽
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false

  const files = event.dataTransfer?.files
  if (files) {
    handleFiles(Array.from(files))
  }
}

// 处理文件
const handleFiles = async (files: File[]) => {
  for (const file of files) {
    if (!validateFile(file)) {
      continue
    }

    try {
      store.setLoading(true)
      const contractFile = await uploadContract(file)
      store.addFile(contractFile)
      message.success(`${file.name} 上传成功！`)
    } catch (error) {
      message.error(`${file.name} 上传失败`)
      console.error('Upload error:', error)
    } finally {
      store.setLoading(false)
    }
  }
}

// 验证文件
const validateFile = (file: File): boolean => {
  const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png']

  if (!validTypes.includes(file.type)) {
    message.error(`${file.name}: 不支持的文件格式`)
    return false
  }

  if (file.size > 50 * 1024 * 1024) {
    message.error(`${file.name}: 文件大小超过50MB`)
    return false
  }

  return true
}
</script>

<style scoped>
.file-upload-area {
  margin-bottom: 32px;
}

.upload-zone {
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background: #fafafa;
  padding: 80px 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-zone:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-zone.drag-over {
  border-color: #1890ff;
  background: #e6f7ff;
  transform: scale(1.02);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  font-size: 48px;
  color: #bfbfbf;
  transition: color 0.3s ease;
}

.upload-zone:hover .upload-icon {
  color: #1890ff;
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.primary-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.secondary-text {
  font-size: 14px;
  color: #999;
}

@media (max-width: 768px) {
  .upload-zone {
    padding: 40px 20px;
    min-height: 150px;
  }

  .upload-icon {
    font-size: 36px;
  }

  .primary-text {
    font-size: 14px;
  }

  .secondary-text {
    font-size: 12px;
  }
}
</style>