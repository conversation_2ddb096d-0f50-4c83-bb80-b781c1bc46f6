<template>
  <div class="file-list-section">
    <div class="section-header">
      <file-text-outlined />
      <span>待审核文件对比表</span>
    </div>
    
    <div v-if="uploadedFiles.length > 0" class="file-list">
      <div 
        v-for="file in uploadedFiles" 
        :key="file.id"
        class="file-item"
        :class="{ selected: selectedFiles.includes(file.id) }"
        @click="toggleFileSelection(file.id)"
      >
        <div class="file-icon">
          <file-pdf-outlined v-if="file.type.includes('pdf')" />
          <file-word-outlined v-else-if="file.type.includes('doc')" />
          <file-image-outlined v-else />
        </div>
        <div class="file-info">
          <div class="file-name">{{ file.name }}</div>
          <div class="file-meta">{{ formatFileSize(file.size) }} • {{ formatTime(file.uploadTime) }}</div>
        </div>
        <div class="file-status">
          <a-tag v-if="selectedFiles.includes(file.id)" color="blue">已选择</a-tag>
          <a-tag v-else color="default">待选择</a-tag>
        </div>
        <div class="file-actions">
          <a-button type="text" size="small" @click.stop="removeFile(file.id)">
            <delete-outlined />
          </a-button>
        </div>
      </div>
    </div>
    
    <div v-else class="empty-files">
      <a-empty description="暂无上传文件" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>

    <!-- 对比操作 -->
    <div v-if="selectedFiles.length === 2" class="compare-section">
      <a-button 
        type="primary" 
        size="large" 
        @click="startCompare"
        :loading="isLoading"
        class="compare-btn"
      >
        <diff-outlined />
        开始智能对比分析
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { 
  FileTextOutlined, 
  FilePdfOutlined,
  FileWordOutlined,
  FileImageOutlined,
  DeleteOutlined,
  DiffOutlined
} from '@ant-design/icons-vue'
import { useContractCompareStore } from '@/stores/contractCompare'
import { compareContracts, getRiskAssessment } from '@/api/contractCompare'

const store = useContractCompareStore()
const selectedFiles = ref<string[]>([])

const uploadedFiles = computed(() => store.uploadedFiles)
const isLoading = computed(() => store.isLoading)

// 切换文件选择
const toggleFileSelection = (fileId: string) => {
  const index = selectedFiles.value.indexOf(fileId)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else if (selectedFiles.value.length < 2) {
    selectedFiles.value.push(fileId)
  } else {
    message.warning('最多只能选择两个文件进行对比')
  }
}

// 移除文件
const removeFile = (fileId: string) => {
  store.removeFile(fileId)
  const index = selectedFiles.value.indexOf(fileId)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  }
  message.info('文件已移除')
}

// 开始对比
const startCompare = async () => {
  if (selectedFiles.value.length !== 2) {
    message.warning('请选择两个文件进行对比')
    return
  }
  
  try {
    store.setLoading(true)
    store.setError(null)
    
    const [file1Id, file2Id] = selectedFiles.value
    const compareResult = await compareContracts(file1Id, file2Id)
    store.setCompareResult(compareResult)
    
    // 获取风险评估
    const riskAssessment = await getRiskAssessment(compareResult.id)
    store.setRiskAssessment(riskAssessment)
    
    message.success('对比分析完成！')
  } catch (error) {
    store.setError('对比失败，请重试')
    message.error('对比失败，请重试')
    console.error('Compare error:', error)
  } finally {
    store.setLoading(false)
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (timeString: string): string => {
  return new Date(timeString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.file-list-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 16px;
}

.file-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background: #fafbfc;
}

.file-item.selected {
  background: rgba(24, 144, 255, 0.05);
  border-left: 4px solid #1890ff;
}

.file-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 16px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #718096;
}

.file-status {
  margin-right: 16px;
}

.file-actions {
  margin-left: 8px;
}

.empty-files {
  text-align: center;
  padding: 48px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.compare-section {
  text-align: center;
  padding: 32px;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 32px;
}

.compare-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 500;
}
</style>
