<template>
  <a-card title="风险评估" :bordered="false" class="risk-assessment">
    <div v-if="riskAssessment" class="assessment-content">
      <!-- 整体风险等级 -->
      <div class="overall-risk">
        <h4>整体风险等级</h4>
        <div class="risk-level" :class="`risk-${riskAssessment.overallRisk}`">
          <component :is="getRiskIcon(riskAssessment.overallRisk)" class="risk-icon" />
          <span class="risk-text">{{ getRiskText(riskAssessment.overallRisk) }}</span>
        </div>
      </div>

      <a-divider />

      <!-- 风险因素 -->
      <div class="risk-factors">
        <h4>风险因素分析</h4>
        <div class="factors-list">
          <div 
            v-for="factor in riskAssessment.riskFactors" 
            :key="factor.factor"
            class="factor-item"
            :class="`factor-${factor.level}`"
          >
            <div class="factor-header">
              <div class="factor-name">
                <component :is="getRiskIcon(factor.level)" class="factor-icon" />
                {{ factor.factor }}
              </div>
              <a-tag :color="getRiskColor(factor.level)">
                {{ getRiskText(factor.level) }}
              </a-tag>
            </div>
            <div class="factor-description">
              {{ factor.description }}
            </div>
          </div>
        </div>
      </div>

      <a-divider />

      <!-- 建议措施 -->
      <div class="recommendations">
        <h4>建议措施</h4>
        <div class="recommendations-list">
          <div 
            v-for="(recommendation, index) in riskAssessment.recommendations" 
            :key="index"
            class="recommendation-item"
          >
            <div class="recommendation-number">{{ index + 1 }}</div>
            <div class="recommendation-text">{{ recommendation }}</div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <a-space>
          <a-button type="primary" @click="generateReport">
            <file-text-outlined />
            生成风险报告
          </a-button>
          <a-button @click="exportAssessment">
            <export-outlined />
            导出评估
          </a-button>
        </a-space>
      </div>
    </div>

    <div v-else class="no-assessment">
      <a-empty 
        description="暂无风险评估数据"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { 
  ExclamationCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'
import { useContractCompareStore } from '@/stores/contractCompare'

const store = useContractCompareStore()

const riskAssessment = computed(() => store.riskAssessment)

// 获取风险等级图标
const getRiskIcon = (level: string) => {
  const icons = {
    high: ExclamationCircleOutlined,
    medium: WarningOutlined,
    low: CheckCircleOutlined
  }
  return icons[level as keyof typeof icons] || WarningOutlined
}

// 获取风险等级颜色
const getRiskColor = (level: string): string => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colors[level as keyof typeof colors] || 'default'
}

// 获取风险等级文本
const getRiskText = (level: string): string => {
  const texts = {
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return texts[level as keyof typeof texts] || '未知'
}

// 生成风险报告
const generateReport = () => {
  message.info('正在生成风险报告...')
  // 这里可以实现生成详细报告的逻辑
}

// 导出评估
const exportAssessment = () => {
  message.info('导出评估功能开发中...')
  // 这里可以实现导出功能
}
</script>

<style scoped>
.risk-assessment {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.assessment-content h4 {
  margin-bottom: 16px;
  color: #333;
  font-weight: 600;
}

.overall-risk {
  text-align: center;
  margin-bottom: 24px;
}

.risk-level {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  border: 2px solid;
}

.risk-high {
  color: #ff4d4f;
  background: #fff2f0;
  border-color: #ff4d4f;
}

.risk-medium {
  color: #faad14;
  background: #fffbe6;
  border-color: #faad14;
}

.risk-low {
  color: #52c41a;
  background: #f6ffed;
  border-color: #52c41a;
}

.risk-icon {
  font-size: 24px;
}

.factors-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.factor-item {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  background: white;
}

.factor-high {
  border-left: 4px solid #ff4d4f;
  background: #fff2f0;
}

.factor-medium {
  border-left: 4px solid #faad14;
  background: #fffbe6;
}

.factor-low {
  border-left: 4px solid #52c41a;
  background: #f6ffed;
}

.factor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.factor-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
}

.factor-icon {
  font-size: 16px;
}

.factor-description {
  color: #666;
  line-height: 1.5;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.recommendation-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.recommendation-text {
  flex: 1;
  color: #333;
  line-height: 1.5;
}

.actions {
  margin-top: 24px;
  text-align: center;
}

.no-assessment {
  text-align: center;
  padding: 48px 24px;
}

@media (max-width: 768px) {
  .factor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .recommendation-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .recommendation-number {
    align-self: flex-start;
  }
}
</style>
