<template>
  <div class="risk-overview-section">
    <div class="section-header">
      <bar-chart-outlined />
      <span>风险统计概览</span>
    </div>
    
    <div class="stats-grid">
      <div class="stat-card high-risk">
        <div class="stat-number">{{ statistics?.riskDistribution.high || 3 }}</div>
        <div class="stat-label">高风险条款</div>
      </div>
      
      <div class="stat-card medium-risk">
        <div class="stat-number">{{ statistics?.riskDistribution.medium || 7 }}</div>
        <div class="stat-label">中等风险条款</div>
      </div>
      
      <div class="stat-card low-risk">
        <div class="stat-number">{{ statistics?.riskDistribution.low || 12 }}</div>
        <div class="stat-label">低风险条款</div>
      </div>
      
      <div class="stat-card overall">
        <div class="stat-number">{{ overallCompliance }}%</div>
        <div class="stat-label">整体合规度</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { BarChartOutlined } from '@ant-design/icons-vue'
import { useContractCompareStore } from '@/stores/contractCompare'

const store = useContractCompareStore()

const compareResult = computed(() => store.compareResult)
const statistics = computed(() => compareResult.value?.statistics)

// 计算整体合规度
const overallCompliance = computed(() => {
  if (!statistics.value) return 89 // 默认值
  
  const { high, medium, low } = statistics.value.riskDistribution
  const total = high + medium + low
  
  if (total === 0) return 100
  
  // 高风险权重0.1，中风险权重0.5，低风险权重0.9
  const weightedScore = (high * 0.1 + medium * 0.5 + low * 0.9) / total
  return Math.round(weightedScore * 100)
})
</script>

<style scoped>
.risk-overview-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
}

.stat-card {
  text-align: center;
  padding: 24px 16px;
  border-radius: 8px;
  background: #fafbfc;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.high-risk {
  background: rgba(229, 62, 62, 0.1);
}

.stat-card.medium-risk {
  background: rgba(246, 173, 85, 0.1);
}

.stat-card.low-risk {
  background: rgba(56, 161, 105, 0.1);
}

.stat-card.overall {
  background: rgba(24, 144, 255, 0.1);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 8px;
}

.stat-card.high-risk .stat-number {
  color: #E53E3E;
}

.stat-card.medium-risk .stat-number {
  color: #F6AD55;
}

.stat-card.low-risk .stat-number {
  color: #38A169;
}

.stat-card.overall .stat-number {
  color: #1890ff;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 20px 16px;
  }
  
  .stat-number {
    font-size: 28px;
  }
}
</style>
