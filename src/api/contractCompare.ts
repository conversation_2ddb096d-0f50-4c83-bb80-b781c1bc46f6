import type { ContractFile, CompareResult, RiskAssessment } from '@/types/contract'

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 上传合同文件
export const uploadContract = async (file: File): Promise<ContractFile> => {
  await delay(1000) // 模拟上传延迟
  
  return {
    id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: file.name,
    type: file.type,
    size: file.size,
    uploadTime: new Date().toISOString()
  }
}

// 对比两个合同文件
export const compareContracts = async (file1Id: string, file2Id: string): Promise<CompareResult> => {
  await delay(2000) // 模拟对比处理延迟
  
  // 模拟对比结果数据
  const mockDifferences = [
    {
      id: 'diff_1',
      type: 'modified' as const,
      content: '合同期限从12个月修改为24个月',
      originalContent: '合同期限：12个月',
      riskLevel: 'medium' as const,
      position: { line: 15, column: 1 },
      description: '合同期限延长可能增加履约风险'
    },
    {
      id: 'diff_2',
      type: 'added' as const,
      content: '新增违约金条款：违约方需支付合同总额10%的违约金',
      riskLevel: 'high' as const,
      position: { line: 28, column: 1 },
      description: '新增的违约金条款可能对我方不利'
    },
    {
      id: 'diff_3',
      type: 'deleted' as const,
      content: '删除了争议解决条款',
      riskLevel: 'high' as const,
      position: { line: 45, column: 1 },
      description: '缺少争议解决机制可能导致纠纷处理困难'
    }
  ]

  const statistics = {
    totalChanges: mockDifferences.length,
    addedCount: mockDifferences.filter(d => d.type === 'added').length,
    deletedCount: mockDifferences.filter(d => d.type === 'deleted').length,
    modifiedCount: mockDifferences.filter(d => d.type === 'modified').length,
    riskDistribution: {
      high: mockDifferences.filter(d => d.riskLevel === 'high').length,
      medium: mockDifferences.filter(d => d.riskLevel === 'medium').length,
      low: mockDifferences.filter(d => d.riskLevel === 'low').length
    }
  }

  return {
    id: `compare_${Date.now()}`,
    file1: { id: file1Id, name: '合同A.pdf', type: 'application/pdf', size: 1024000, uploadTime: new Date().toISOString() },
    file2: { id: file2Id, name: '合同B.pdf', type: 'application/pdf', size: 1024000, uploadTime: new Date().toISOString() },
    differences: mockDifferences,
    statistics,
    compareTime: new Date().toISOString(),
    status: 'completed'
  }
}

// 获取风险评估
export const getRiskAssessment = async (compareId: string): Promise<RiskAssessment> => {
  await delay(1000)
  
  return {
    overallRisk: 'medium',
    riskFactors: [
      {
        factor: '违约金条款',
        level: 'high',
        description: '新增的违约金比例较高，建议协商降低'
      },
      {
        factor: '争议解决',
        level: 'high',
        description: '缺少争议解决条款，建议补充仲裁或诉讼条款'
      },
      {
        factor: '合同期限',
        level: 'medium',
        description: '期限延长需要评估长期履约能力'
      }
    ],
    recommendations: [
      '建议将违约金比例从10%降低至5%',
      '补充争议解决条款，建议采用仲裁方式',
      '对延长的合同期限进行风险评估',
      '建议增加合同变更和终止条款'
    ]
  }
}
