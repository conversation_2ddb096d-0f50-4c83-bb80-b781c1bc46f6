import { createRouter, createWebHistory } from 'vue-router'
import ContractCompare from '@/views/ContractCompare/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/contract-compare'
    },
    {
      path: '/contract-compare',
      name: 'contract-compare',
      component: ContractCompare
    }
  ]
})

export default router
