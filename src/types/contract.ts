// 合同相关类型定义

export interface ContractFile {
  id: string
  name: string
  type: string
  size: number
  url?: string
  uploadTime: string
}

export interface ContractDifference {
  id: string
  type: 'added' | 'deleted' | 'modified'
  content: string
  originalContent?: string
  riskLevel: 'high' | 'medium' | 'low'
  position: {
    line: number
    column: number
  }
  description: string
}

export interface CompareStatistics {
  totalChanges: number
  addedCount: number
  deletedCount: number
  modifiedCount: number
  riskDistribution: {
    high: number
    medium: number
    low: number
  }
}

export interface CompareResult {
  id: string
  file1: ContractFile
  file2: ContractFile
  differences: ContractDifference[]
  statistics: CompareStatistics
  compareTime: string
  status: 'processing' | 'completed' | 'failed'
}

export interface RiskAssessment {
  overallRisk: 'high' | 'medium' | 'low'
  riskFactors: Array<{
    factor: string
    level: 'high' | 'medium' | 'low'
    description: string
  }>
  recommendations: string[]
}
