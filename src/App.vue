<script setup lang="ts">
import { RouterView } from 'vue-router'
import Sidebar from '@/components/Sidebar.vue'
</script>

<template>
  <div class="app">
    <a-layout class="app-layout">
      <!-- 侧边栏 -->
      <Sidebar />

      <!-- 主内容区域 -->
      <a-layout class="main-layout">
        <a-layout-content class="main-content">
          <RouterView />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: #f5f7fa;
}

#app {
  min-height: 100vh;
}

.app {
  min-height: 100vh;
}

.app-layout {
  min-height: 100vh;
}

.main-layout {
  margin-left: 240px;
  min-height: 100vh;
}

.main-content {
  background: #f5f7fa;
  padding: 0;
  min-height: 100vh;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .main-layout {
    margin-left: 80px;
  }
}
</style>
