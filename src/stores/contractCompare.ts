import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface UploadedFile {
  id: string
  name: string
  type: 'pdf' | 'doc' | 'image'
  size: string
  uploadTime: string
}

export interface CompareResult {
  differences: Array<{
    type: 'added' | 'deleted' | 'modified'
    content: string
    riskLevel: 'high' | 'medium' | 'low'
    position: { line: number, section: string }
  }>
  statistics: {
    totalChanges: number
    addedClauses: number
    deletedClauses: number
    modifiedClauses: number
  }
}

export const useContractCompareStore = defineStore('contractCompare', () => {
  const uploadedFiles = ref<UploadedFile[]>([])
  const compareResult = ref<CompareResult | null>(null)
  const loading = ref(false)

  const addUploadedFile = (file: UploadedFile) => {
    uploadedFiles.value.push(file)
  }

  const removeUploadedFile = (fileId: string) => {
    const index = uploadedFiles.value.findIndex(f => f.id === fileId)
    if (index > -1) {
      uploadedFiles.value.splice(index, 1)
    }
  }

  const compareContracts = async (fileId1: string, fileId2: string) => {
    loading.value = true
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      compareResult.value = {
        differences: [
          {
            type: 'modified',
            content: '付款期限从30天修改为45天',
            riskLevel: 'medium',
            position: { line: 15, section: '第三条 付款条款' }
          },
          {
            type: 'added',
            content: '新增违约金条款：逾期付款按日收取0.05%违约金',
            riskLevel: 'high',
            position: { line: 28, section: '第五条 违约责任' }
          }
        ],
        statistics: {
          totalChanges: 22,
          addedClauses: 5,
          deletedClauses: 3,
          modifiedClauses: 14
        }
      }
    } finally {
      loading.value = false
    }
  }

  return {
    uploadedFiles,
    compareResult,
    loading,
    addUploadedFile,
    removeUploadedFile,
    compareContracts
  }
})
